package com.sailing.controller.northbound;

import com.sailing.common.OperateMenuExt;
import com.sailing.entity.OperateType;
import com.sailing.service.TEventWarnService;
import com.sailing.util.PageUtil;
import com.sailing.vo.AssetListReq;
import com.sailing.vo.BizStatisticsReq;
import com.sailing.vo.BizStatisticsResp;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 北向接口/业务信息
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@RestController
@RequestMapping("biz")
@RequiredArgsConstructor
@CrossOrigin
public class BizAssetController {

    @Resource
    public TEventWarnService tEventWarnService;
    /**
     * 资产列表查询
     *
     * @param req 资产列表查询参数
     * @return 查询结构
     */
    @ApiOperation(value = "资产列表查询", notes = "资产列表查询")
    @PostMapping(value = "asset/list")
    @OperateType(menu = OperateMenuExt.MENU_BIZ_MANAGE, operateType = OperateMenuExt.OPERATE_LIST)
    public PageUtil list(@RequestBody @Validated AssetListReq req) {
        return tEventWarnService.deviceList(req);
    }

    /**
     *  业务和当日流量统计信息查询
     *
     * @param req 业务和当日流量统计信息查询
     * @return 查询结果
     */
    @ApiOperation(value = "业务和当日流量统计信息查询", notes = "业务和当日流量统计信息查询")
    @PostMapping(value = "statistics/query")
    @OperateType(menu = OperateMenuExt.MENU_BIZ_MANAGE, operateType = OperateMenuExt.OPERATE_LIST)
    public List<BizStatisticsResp> bizStatistics(@RequestBody @Validated BizStatisticsReq req) {
        return tEventWarnService.bizStatistics(req);
    }
}
