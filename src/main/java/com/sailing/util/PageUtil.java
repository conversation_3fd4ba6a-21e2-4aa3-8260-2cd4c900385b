package com.sailing.util;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 分页工具类
 * <AUTHOR>
 * @date 2024/5/12 15:15
 * @Version 1.0
 */
public class PageUtil implements Serializable {
    private static final long serialVersionUID = 1L;
    //总记录数
    private long total;
    //每页记录数(同pageNum，部分接口用的是current)
    private int current;
    //每页记录数
    private int pageSize;
    //当前页数
    private int pageNumber;
    //列表数据
    private List<?> data;

    /**
     * 分页
     *
     * @param data     列表数据
     * @param total    总记录数
     * @param pageSize 每页记录数
     * @param pageNumber  当前页数
     */
    public PageUtil(List<?> data, long total, int pageSize, int pageNumber) {
        this.data = data;
        this.total = total;
        this.pageSize = pageSize;
        this.pageNumber = pageNumber;
        this.current = pageNumber;
    }


    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public List<?> getData() {
        return data;
    }

    public void setData(List<?> data) {
        this.data = data;
    }

}
