package com.sailing.entity;
import lombok.Data;


@Data
public class AlarmAggregation {
    /**
     * ID
     */
    public Long id;

    /**
     * 状态: 0-禁用, 1-启用
     */
    public int status;

    /**
     * 聚合规则配置
     */
    public String config;

    /**
     * 创建时间
     */
    public String createTime;

    /**
     * 更新时间
     */
    public String updateTime;

    /**
     * 备注
     */
    public String remarks;

    public String aggregationRule;


}
