package com.sailing.entity;

import lombok.Data;


@Data
public class AlarmMidAggregation {
    /**
     * ID
     */
    public Long id;

    /**
     * 安全事件id
     */
    public Integer alarmEventId;

    /**
     * 告警id
     */
    public Integer alarmId;
    /**
     * 创建时间
     */
    public String createTime;

    /**
     * 更新时间
     */
    public String updateTime;

    /**
     * 告警聚合ids
     */
    public String alarmIds;

    /**
     * 站点编号
     */
    public String uniPositionCode;

    public String alarmType;

    public String appId;

    public String linkId;

    public String deviceIp;

}
