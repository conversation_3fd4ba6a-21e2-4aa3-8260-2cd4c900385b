package com.sailing.nta;

import com.sailing.entity.NetflowNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/24 15:40
 * @Description:
 **/
@Mapper
@Repository
public interface ICmAppDao {
    @Select({"<script>" +
            "select * from t_netflow_node where nodeStatus = '1'" +
            "</script>"})
    List<NetflowNode> listAll();

    @Select({"<script>" +
            "<if test=\"list != null and list.size() != 0\">" +
            "select * from t_netflow_node " +
            "where id in" +
            "<foreach collection=\"list\" open=\"(\" close=\")\" item=\"item\" separator=\", \" index=\"index\">" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>"})
    List<NetflowNode> getById(@Param("list") List<String> idList);

    @Select({"<script>" +
            "select * from t_netflow_node where id = #{id}" +
            "</script>"})
    NetflowNode getBySingleId(String id);

    @Select({"<script>" +
            "select id from t_netflow_node where nodeStatus = '1' limit 1" +
            "</script>"})
    NetflowNode getNetFlowStatus();

    @Select({"<script>" +
            " select * from t_netflow_node " +
            "</script>"})
    List<NetflowNode> getNetFlowNodeList();
    /**
     *  业务当日流量统计信息查询
     *
     * @param req 业务当日流量统计信息查询
     * @return 查询结果
     */
    @ApiOperation(value = "资产列表查询", notes = "资产列表查询")
    @PostMapping(value = "statistics/query")
    @OperateType(menu = OperateMenuExt.MENU_BIZ_MANAGE, operateType = OperateMenuExt.OPERATE_LIST)
    public List<BizStatisticsResp> bizStatistics(@RequestBody @Validated BizStatisticsReq req) {
        return tEventWarnService.bizStatistics(req);
    }
}
