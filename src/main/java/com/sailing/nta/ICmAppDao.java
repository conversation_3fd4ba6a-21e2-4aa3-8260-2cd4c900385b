package com.sailing.nta;

import com.sailing.entity.NetflowNode;
import com.sailing.vo.LinkDayFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/24 15:40
 * @Description:
 **/
@Mapper
@Repository
public interface ICmAppDao {
    @Select({"<script>" +
            "select * from t_netflow_node where nodeStatus = '1'" +
            "</script>"})
    List<NetflowNode> listAll();

    @Select({"<script>" +
            "<if test=\"list != null and list.size() != 0\">" +
            "select * from t_netflow_node " +
            "where id in" +
            "<foreach collection=\"list\" open=\"(\" close=\")\" item=\"item\" separator=\", \" index=\"index\">" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>"})
    List<NetflowNode> getById(@Param("list") List<String> idList);

    @Select({"<script>" +
            "select * from t_netflow_node where id = #{id}" +
            "</script>"})
    NetflowNode getBySingleId(String id);

    @Select({"<script>" +
            "select id from t_netflow_node where nodeStatus = '1' limit 1" +
            "</script>"})
    NetflowNode getNetFlowStatus();

    @Select({"<script>" +
            " select * from t_netflow_node " +
            "</script>"})
    List<NetflowNode> getNetFlowNodeList();

    @Select("<script>" +
        "SELECT linkId, SUM(sumFlow) AS dataFlow FROM t_app_flow " +
        "WHERE 1 = 1 " +
        "<if test='queryTime != null and queryTime != \"\"'>" +
        "and to_days(plusTime) = to_days(#{queryTime}) " +
        "</if>" +
        "GROUP BY linkId;" +
        "</script>")
    List<LinkDayFlow> queryLinkDayFlow(@Param("queryTime") String queryTime);
}
