package com.sailing.dao;

import com.sailing.entity.AlarmAggregation;
import com.sailing.entity.AlarmMidAggregation;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface IAlarmAggregationDao {

    @Select({"<script>" +
            "select *  from t_alarm_aggregation_rule  " +
            "</script>"})
    List<AlarmAggregation> getAlarmAggregation();


    @Select({"<script>" +
            "select m.*  from  t_alarm_mid_aggregation m      " +
            "<where>" +
            " and TO_DAYS(m.updateTime) = TO_DAYS(now())   "+
            "<if test=\"alarmType!=null and alarmType!=''\">" +
            " and  m.alarmType like CONCAT('%',#{alarmType},'%')" +
            "</if>" +
            "<if test=\"deviceIp!=null and deviceIp!=''\">" +
            " and deviceIp = #{deviceIp} " +
            "</if>" +
            "</where>" +
            "</script>"})
    List<AlarmMidAggregation> getAlarmMidAggregation(@Param("alarmType")String alarmType, @Param("deviceIp")String deviceIp);

    @Update({"<script>" +
            " update t_alarm_mid_aggregation " +
            "<set>" +
            "<if test=\"alarmId!=null and alarmId!=''\">" +
            " alarmId = #{alarmId}," +
            "</if>" +
            "<if test=\"alarmIds!=null and alarmIds!=''\">" +
            " alarmIds = #{alarmIds}," +
            "</if>" +
            "<if test=\"appId!=null and appId!=''\">" +
            " appId = #{appId}," +
            "</if>" +
            "<if test=\"linkId!=null and linkId!=''\">" +
            " linkId = #{linkId}," +
            "</if>" +
            "<if test=\"deviceIp!=null and deviceIp!=''\">" +
            " deviceIp = #{deviceIp}," +
            "</if>" +
            " updateTime =now()  "+
            "</set>" +
            " where id = #{id} " +
            "</script>"})
    void updateAlarmMidAggregation(AlarmMidAggregation aggregatedEvent);


    @Insert({"<script>" +
            "insert into t_alarm_mid_aggregation(uniPositionCode, alarmEventId, alarmId, alarmIds, alarmType, appId, linkId, deviceIp, createTime, updateTime) " +
            "values(#{uniPositionCode}, #{alarmEventId}, #{alarmId}, #{alarmIds}, #{alarmType}, #{appId}, #{linkId}, #{deviceIp}, now(), now())" +
            "</script>"})
    void saveAlarmMidAggregation(AlarmMidAggregation aggregatedEvent);

    @Select({"<script>" +
            " SELECT  GROUP_CONCAT(DISTINCT CONCAT_WS(',', alarmId, alarmIds) SEPARATOR ',')  as alarmIds FROM  t_alarm_mid_aggregation where TO_DAYS(updateTime) = TO_DAYS(now()) " +
            "</script>"})
    String getTodayAlarmIds();

    @Select({"<script>" +
            " SELECT  GROUP_CONCAT(DISTINCT  alarmIds)  as alarmIds FROM  t_alarm_mid_aggregation " +
            "</script>"})
    String getAlarmIds();
}
