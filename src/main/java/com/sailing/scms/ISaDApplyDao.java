package com.sailing.scms;

import com.sailing.dto.DutyPersonInfoDTO;
import com.sailing.vo.BizStatisticsResp;
import com.sailing.vo.LinkWithDomain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ISaDApplyDao {

    /**
     * 根据业务类型获取符合条件的业务id
     *
     * @param type 业务类型（业务操作方式）
     * @return 符合条件的id集合
     */
    @Select("SELECT id FROM sa_d_apply WHERE applicationMode = #{type}")
    List<Integer> listIdByBizType(@Param("type") Integer type);

    @Select("select a.id from t_label l LEFT JOIN sa_d_apply a on FIND_IN_SET(l.id,a.labelId) " +
        " where a.labelId is not null and l.name like concat('%',#{labelName},'%') GROUP BY a.id")
    List<Integer> listIdByLabelName(@Param("labelName")String labelName);

    @Select({"<script>" +
            "select GROUP_CONCAT(systemName) as name from sa_d_apply where FIND_IN_SET(appCode,#{appCodes})  "+
            "</script>"})
    String getAppNameByCodes(@Param("appCodes") String appCodes);

    @Select({"<script>" +
            "select GROUP_CONCAT(appCode) as name from sa_d_apply where systemName like concat('%',#{systemName},'%')  "+
            "</script>"})
    String getAppCodesByName(@Param("systemName") String systemName);

    @Select({"<script>" +
            "SELECT competentName as dutyName, competentPhone as dutyMobile " +
            "FROM `sa_d_apply` " +
            "WHERE id in  " +
            "<foreach collection=\"ids\" open=\"(\" separator=\",\" close=\")\" item=\"id\"> " +
            "#{id}" +
            "</foreach>" +
            "</script>"})
    List<DutyPersonInfoDTO> getDutyPersonInfo(@Param("ids") List<String> ids);

    @Select("<script>" +
        "SELECT COALESCE(COUNT(app.id), 0) AS bizCount, NOW() AS time, COALESCE(COUNT(link.id), 0) AS linkCount, " +
        "COALESCE(domain.name, '未分类') AS domain FROM cm_link link " +
        "LEFT JOIN t_domain domain ON link.domainId = domain.id " +
        "LEFT JOIN sa_d_apply app ON link.id = app.device_no " +
        "<if test='domain != null and domain != \"\"'>" +
        "WHERE domain.name = #{domain}" +
        "</if>" +
        "GROUP BY domain.name" +
        "</script>")
    List<BizStatisticsResp> queryAppAndLinkCountByDomain(@Param("domain") String domain);

    /**
     * 查询链路与域的关联信息
     *
     * @param domain 域名，可为空
     * @return 链路与域的关联信息列表
     */
    @Select("<script>" +
        "SELECT DISTINCT link.id AS linkId, link.link_name AS linkName, domain.name AS domain, domain.id AS domainId " +
        "FROM cm_link link LEFT JOIN t_domain domain ON link.domainId = domain.id " +
        "WHERE 1=1 " +
        "<if test='domain != null and domain != \"\"'>" +
        "AND domain.name = #{domain} " +
        "</if>" +
        "ORDER BY domain.name, link.id" +
        "</script>")
    List<LinkWithDomain> queryLinkWithDomain(@Param("domain") String domain);
}
