package com.sailing.scms;

import com.sailing.vo.BizStatisticsResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ISaDApplyDao {

    /**
     * 根据业务类型获取符合条件的业务id
     *
     * @param type 业务类型（业务操作方式）
     * @return 符合条件的id集合
     */
    @Select("SELECT id FROM sa_d_apply WHERE applicationMode = #{type}")
    List<Integer> listIdByBizType(@Param("type") Integer type);

    @Select("select a.id from t_label l LEFT JOIN sa_d_apply a on FIND_IN_SET(l.id,a.labelId) " +
        " where a.labelId is not null and l.name like concat('%',#{labelName},'%') GROUP BY a.id")
    List<Integer> listIdByLabelName(@Param("labelName")String labelName);

    @Select({"<script>" +
            "select GROUP_CONCAT(systemName) as name from sa_d_apply where FIND_IN_SET(appCode,#{appCodes})  "+
            "</script>"})
    String getAppNameByCodes(@Param("appCodes") String appCodes);

    @Select({"<script>" +
            "select GROUP_CONCAT(appCode) as name from sa_d_apply where systemName like concat('%',#{systemName},'%')  "+
            "</script>"})
    String getAppCodesByName(@Param("systemName") String systemName);

    @Select("<script>" +
            "select id,linkId,appId,inFlow,outFlow,sumFlow,inCount,outCount,sumCount,positionCode,plusTime " +
            "from t_app_flow " +
            "<where>" +
            "<if test='appIds !=null and appIds.size()>0'> " +
            "and appId in " +
            "<foreach collection='appIds' item='appId' index='index' open='(' close=')' separator=','>" +
            "#{appId} " +
            "</foreach>" +
            "</if>" +
            "<if test='plusTime!=null'>" +
            "and to_days(plusTime)=to_days(#{plusTime}) " +
            "</if>" +
            "</where>" +
            " GROUP BY appId " +
            "</script>")
    List<BizStatisticsResp> findAppFlowsByAppIds(@Param("appIds") List<Integer> appIds, @Param("plusTime") String plusTime);
}
