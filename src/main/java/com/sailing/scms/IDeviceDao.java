package com.sailing.scms;

import com.sailing.entity.CmDevLink;
import com.sailing.entity.EventAsset;
import com.sailing.entity.SaDAppTable;
import com.sailing.entity.SadApp;
import com.sailing.vo.AssetListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IDeviceDao {

	@Select({"<script>" +
			" select dev.id as devId,dev.link_id as linkId,link.link_name as linkName ,link.linkSubject as linkSubject ,dev.dev_name as devName,dev.dev_ip1 as devIp," +
		    " dev.dev_ip2 as devIp2,dev.dev_ip3 as devIp3,dev.dev_ip4 as devIp4,dev.uniPositionCode as uniPositionCode  " +
			" from cm_device dev " +
			" left join cm_link link on dev.link_id=link.id " +
			" and dev.uniPositionCode=link.uniPositionCode " +
			" where dev.is_linkip=1 " +
			"<if test='ips!=null and ips.size()>0'> " +
			" and (dev.dev_ip1 IN " +
			"<foreach collection='ips' item='ip' index='index' open='(' close=')' separator=','>" +
			" #{ip} " +
			"</foreach>" +
			")"+
			" or (dev.dev_ip4 IN " +
			"<foreach collection='ips' item='ip' index='index' open='(' close=')' separator=','>" +
			" #{ip} " +
			"</foreach>" +
			")"+
			"</if> " +
			"</script>"})
	List<CmDevLink> queryDevLink(@Param("ips")List<String> ips);

	@Select({"<script>" +
			" select * from sa_d_apptable where appCode=#{appCode}" +
			"</script>"})
	List<SaDAppTable> queryAppTable(@Param("appCode") String appCode);

	@Select({"<script> " +
			"select app.id as id,app.pattern as pattern,app.outPattern as outPattern," +
			" app.dataExchangeDirection as dataExchangeDirection,app.device_no as device_no," +
			" link.link_name as link_name,link.linkSubject as linkSubject, " +
			"app.appCode as appCode,app.systemName as systemName,col.destinyIpRange as destinyIpRange " +
			"from sa_d_apply  app " +
			"left join sa_d_applyprotocol col on  app.appCode=col.appcode " +
			" and app.uniPositionCode=col.uniPositionCode " +
			"left join cm_link link ON app.device_no=link.id  " +
			" and app.uniPositionCode=link.uniPositionCode " +
			"where app.applicationMode=#{applicationMode} " +
			" and app.uniPositionCode=#{uniPositionCode}" +
			"  and app.onlineStatus=0  "+
			"</script> "})
	List<SadApp> getSaApps(@Param(value = "applicationMode") String applicationMode, @Param("uniPositionCode") String uniPositionCode);

	@Select("<script>" +
			"select dev_ip1 from cm_device where is_linkip='1' and link_id=#{link_id} " +
			" and uniPositionCode=#{uniPositionCode}" +
			"</script>")
	List<String> getSwapDeviceDevip1(@Param("link_id") String linkId, @Param("uniPositionCode") String uniPositionCode);

	@Select({"<script>" +
			" select GROUP_CONCAT(DISTINCT CONCAT_WS(',', IF(device.dev_ip1 IS NOT NULL, device.dev_ip1, NULL), IF(device.dev_ip4 IS NOT NULL, device.dev_ip4, NULL) ) SEPARATOR ',')  ip from cm_link link left join cm_device device " +
			" on device.link_id=link.id where device.is_linkip='1' and (device.dev_ip1 is not null or device.dev_ip4 is not null)  " +
			"<if test='linkCodes!=null and linkCodes.size()>0'> " +
			" and link.linkCode IN " +
			"<foreach collection='linkCodes' item='linkCode' index='index' open='(' close=')' separator=','>" +
			" #{linkCode} " +
			"</foreach>" +
			"</if> " +
			"</script>"})
	String getBorderIps(@Param("linkCodes") List<String> linkCodes);

	@Select("select link_id from cm_device where  1=1 (dev_ip1=#{devIp} or dev_ip4=#{devIp}) limit 1")
	String linkCode(@Param("devIp") String devIp);

	@Select({"<script>" +
			" select concat_ws(',',device.dev_ip1,device.dev_ip2,device.dev_ip3,device.dev_ip4) as ip from cm_device device " +
			" where device.is_linkip='1' and device.dev_ip1 is not null " +
			"<if test='devIp!=null'> " +
			" and  (device.dev_ip1=#{devIp} or device.dev_ip2=#{devIp} or device.dev_ip3=#{devIp} or device.dev_ip4=#{devIp})" +
			"</if> " +
			"</script>"})
	List<String> getDeviceIp(@Param("devIp")String srcIp);


	@Select({"<script>" +
			"select GROUP_CONCAT(deviceCode) as deviceCode from cm_device where dev_name  like concat('%', #{deviceName},'%')   "+
			"</script>"})
	String getDeviceCodeByName(@Param("deviceName")String deviceCodes);
	@Select({"<script>" +
			"select GROUP_CONCAT(dev_name) as name from cm_device where FIND_IN_SET(deviceCode,#{deviceCodes})  "+
			"</script>"})
    String getDeviceNameByCodes(@Param("deviceCodes")String deviceCodes);

	@Select({"<script>" +
			"select  d.deviceCode,d.dev_name as deviceName,l.linkCode,l.link_name as linkName,s.appCode,s.systemName as appName from cm_device d  " +
			"LEFT JOIN cm_link l on d.linkCode=l.linkCode  " +
			"LEFT JOIN sa_d_apply s on l.linkCode=s.linkCode " +
			"where FIND_IN_SET(d.deviceCode,#{deviceCodes})  " +
			"<if test='linkCodes!=null'> " +
			" and FIND_IN_SET(l.linkCode,#{linkCodes})  " +
			"</if> " +
			"<if test='appCodes!=null'> " +
			"and FIND_IN_SET(s.appCode,#{appCodes}) "+
			"</if> " +
			"</script>"})
	List<EventAsset> getEventAssetList(@Param("deviceCodes")String deviceCodes, @Param("linkCodes")String linkCodes, @Param("appCodes")String appCodes);

	@Select({"<script>" +
	"select  m.name as domainName,u.uniPositionName,dd.dd_name as deviceType,d.dev_ip1 as devIp,d.dev_type,d.id as devId,d.deviceCode,d.uniPositionCode," +
	"l.id as linkId,l.linkCode,l.link_name as linkName,l.policeName,l.manufacturerPerson, GROUP_CONCAT(s.id) appId,GROUP_CONCAT(s.appCode) appCode from cm_device d  " +
	"LEFT JOIN cm_link l on d.linkCode=l.linkCode  " +
	"LEFT JOIN sa_d_apply s on s.linkCode=l.linkCode  " +
	"LEFT JOIN cm_ddcode dd on dd.item=d.dev_type  " +
	"LEFT JOIN cm_unit u on u.uniPositionCode=l.uniPositionCode  "+
	"LEFT JOIN t_domain m on m.id=l.domainId  "+
	"where dd.`code`='2001' and  FIND_IN_SET(d.dev_type,#{devTypes}) GROUP BY d.id"+
	"</script>"})
	List<CmDevLink> getBorderDeviceList(@Param("devTypes") String devTypes);

	@Select({"<script>" +
		"SELECT " +
		"dev.id AS id," +
		"dev.dev_name AS NAME," +
		"dev.dev_ip1 AS ip," +
		"d.dd_name AS deviceTypeName," +
		"dev.src_mac AS mac," +
		"dev.uniPositionCode," +
		"eq.manufactoryAndType AS deviceVendor," +
		"eq.deviceType AS deviceModel," +
		"dev.sys_version AS osVersion," +
		"dev.agingDate AS maintenanceDay," +
		"dev.useDate AS manufactureDate, " +
		"dev.unit AS entryUnit " +
		"FROM cm_device dev " +
		"LEFT JOIN cm_ddcode d ON d.item = dev.dev_type " +
		"LEFT JOIN sa_s_equipment eq  ON eq.deviceCode = dev.deviceCode " +
		"AND eq.uniPositionCode = dev.uniPositionCode " +
		"GROUP BY dev.id" +
		"</script>"})
	List<AssetListResp> deviceList();
}
