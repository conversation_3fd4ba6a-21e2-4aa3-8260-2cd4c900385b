package com.sailing.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sailing.bld.ITaskResultDao;
import com.sailing.common.CmUnitUtil;
import com.sailing.dao.IAlarmEventDao;
import com.sailing.dao.IAlarmEventStrategyDao;
import com.sailing.dao.IAlarmItemDao;
import com.sailing.dao.IEventWarningDao;
import com.sailing.entity.*;
import com.sailing.entity.bld.BorderTask;
import com.sailing.entity.bld.UnregisteredProperty;
import com.sailing.entity.nta.Sensitivedata;
import com.sailing.nta.ISensitivedataDao;
import com.sailing.scms.*;
import com.sailing.service.IAlarmEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmEventService implements IAlarmEventService {

    private static final String BORDER_ALARM_EVENT_KEY = "borderAlarmEventList";
    @Autowired
    private IAlarmEventDao alarmEventDao;
    @Autowired
    private ICmLinkDao cmLinkDao;
    @Autowired
    private IDeviceDao deviceDao;
    @Autowired
    private ISaDApplyDao saDApplyDao;
    @Autowired
    private IAppDao appDao;
    @Autowired
    private IEventWarningDao eventWarningDao;
    @Autowired
    private ICmUnitDao iCmUtilDao;
    @Autowired
    private IAlarmEventStrategyDao alarmEventStrategyDao;
    @Resource
    private IAlarmItemDao alarmItemDao;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ISensitivedataDao sensitivedataDao;

    @Autowired
    private ITaskResultDao taskResultDao;


    public static String defaultIfBlank(String str) {
        String defaultStr = "--";
        return (str == null || str.trim().isEmpty()) ? defaultStr : str;
    }

    private static Map<String, List<EventWarning>> checkDevicesExist(String eventTypes, Map<String, List<EventWarning>> groupedByLinkId) {
        String[] eventTypeCodes = eventTypes.split(",");
        Map<String, List<EventWarning>> matchedGroups = new HashMap<>();

        for (Map.Entry<String, List<EventWarning>> entry : groupedByLinkId.entrySet()) {
            List<EventWarning> eventWarnings = entry.getValue();
            boolean allDevicesMatched = true;
            boolean contains2001 = false;

            for (String eventType : eventTypeCodes) {
                if (eventType.startsWith("2")) {
                    // 检查是否包含"2001"，只需检查一次
                    if (!contains2001 && eventType.equals("2001")) {
                        contains2001 = eventWarnings.stream()
                                .anyMatch(eventWarning -> String.valueOf(eventWarning.getEventType()).contains("2001"));
                    }
                } else if (eventType.startsWith("1") || eventType.startsWith("3")) {
                    // 对于以"1"或"3"开头的eventType，保持原有逻辑
                    if (!eventWarnings.stream()
                            .anyMatch(eventWarning -> eventType.equals(String.valueOf(eventWarning.getEventType())))) {
                        allDevicesMatched = false;
                        break; // 如果不匹配，则跳出循环
                    }
                }
            }

            // 如果以"2"开头的eventType中没有"2001"，则认为不匹配
            if (!contains2001 && Arrays.stream(eventTypeCodes).anyMatch(et -> et.startsWith("2"))) {
                allDevicesMatched = false;
            }

            if (allDevicesMatched) {
                // 如果当前分组中的所有设备ID都存在，则将该分组添加到matchedGroups中
                matchedGroups.put(entry.getKey(), eventWarnings);
            }
        }
        return matchedGroups; // 返回匹配到的组
    }

    /**
     * @param str              模版
     * @param placeholderIndex 要从第placeholderIndex个；开始截取
     * @return
     */
    private static String substringBeforeThirdSemicolon(String str, int placeholderIndex) {
        // 使用分号分割字符串
        String[] parts = str.split("。");

        // 检查placeholderIndex是否有效
        if (placeholderIndex > 0 && placeholderIndex <= parts.length) {
            // 根据placeholderIndex截取字符串，并使用分号连接
            return String.join("。", Arrays.copyOfRange(parts, 0, placeholderIndex + 1)) + "。";
        }

        // 如果没有找到第三个分号，返回原始字符串
        return str;
    }

    @Override
    public PageHelper<AlarmEventDTO> queryByPage(PageHelper<AlarmEventVO> pageHelper) {
        PageHelper<AlarmEventDTO> pageHelper1 = new PageHelper<>();
        try {
            AlarmEventVO req = pageHelper.getObject();
            Integer current = req.getCurrent();
            req.setCurrent((req.getCurrent() - 1) * req.getPageSize());
            String uniPositionCode = req.getUniPositionCode();
            List<String> uniPositionCodeList = null;
            if (StringUtils.isNotBlank(uniPositionCode)) {
                uniPositionCodeList = Arrays.asList(uniPositionCode.split(","));
            } else {
                // 查询子机构
                uniPositionCodeList = CmUnitUtil.getUniPositionCodeChildren(CmUnitUtil.getUserPreUniPositionCode());
            }
            req.setUniPositionCode(null);
            req.setUniPositionCodeList(uniPositionCodeList);

            if (StringUtils.isNotBlank(req.getAppCodes())) {
                String appCodes = saDApplyDao.getAppCodesByName(req.getAppCodes());
                if (StringUtils.isNotBlank(appCodes)) {
                    req.setAppCodeList(Arrays.asList(appCodes.split(",")));
                }
            }

            if (StringUtils.isNotBlank(req.getDeviceCodes())) {
                String deviceCode = deviceDao.getDeviceCodeByName(req.getDeviceCodes());
                if (StringUtils.isNotBlank(deviceCode)) {
                    req.setDeviceCodeList(Arrays.asList(deviceCode.split(",")));
                }
            }

            List<AlarmEventDTO> list = alarmEventDao.queryList(req);
            Map<String, String> uniPositionMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                List<CmUnit> cmUnits = iCmUtilDao.getAll();
                uniPositionMap =
                        cmUnits.stream().collect(Collectors.toMap(CmUnit::getUniPositionCode, CmUnit::getUniPositionName));
                for (AlarmEventDTO eventDTO : list) {
                    String deviceCodes = eventDTO.getDeviceCodes();
                    String linkCodes = eventDTO.getLinkCodes();
                    String appCodes = eventDTO.getAppCodes();
                    String positionCode = eventDTO.getUniPositionCode();
                    if (StringUtils.isNotBlank(deviceCodes)) {
                        eventDTO.setDeviceCodes(deviceDao.getDeviceNameByCodes(deviceCodes));
                    }
                    if (StringUtils.isNotBlank(linkCodes)) {
                        eventDTO.setLinkCodes(cmLinkDao.getLinkNameByCodes(linkCodes));
                    }
                    if (StringUtils.isNotBlank(appCodes)) {
                        eventDTO.setAppCodes(saDApplyDao.getAppNameByCodes(appCodes));
                    }
                    if (StringUtils.isNotBlank(positionCode) && uniPositionMap.containsKey(positionCode)) {
                        String uniPositionName = uniPositionMap.get(positionCode);
                        eventDTO.setUniPositionName(uniPositionName);
                    }
                }
            }
            pageHelper1.setPageSize(req.getPageSize());
            pageHelper1.setCurrent(current);
            pageHelper1.setObjects(list);
            long total = alarmEventDao.total(req);
            pageHelper1.setTotal(Math.min(100000, total));
        } catch (Exception e) {
            log.error("安全事件列表查询异常！ ", e);
        }
        return pageHelper1;
    }

    /**
     * 更新安全事件状态  0-待处置, 1-已处置, 2-已恢复
     *
     * @param alarmEventVO
     * @return
     */
    @Override
    public int updateStatusList(AlarmEventVO alarmEventVO) {
        return alarmEventDao.updateStatusList(alarmEventVO);
    }

    /**
     * 新增安全事件
     *
     * @param alarmEventDTO
     * @return
     */
    @Override
    public Boolean saveAlarmEvent(AlarmEventDTO alarmEventDTO) {
        Boolean result = alarmEventDao.saveAlarmEvent(alarmEventDTO);
        return result;
    }

    @Override
    public HashMap<String, Object> get(AlarmEventVO alarmEventVO) {
        HashMap<String, Object> map = new HashMap<>();
        // 安全事件基本信息 中包含(安全事件基础信息,事件还原字段,处置建议字段)
        AlarmEventDTO alarmEventDTO = alarmEventDao.get(alarmEventVO);
        if (Objects.isNull(alarmEventDTO)) {
            return map;
        }
        List<CmUnit> cmUnits = iCmUtilDao.getAll();
        Map<String, String> uniPositionMap =
                cmUnits.stream().collect(Collectors.toMap(CmUnit::getUniPositionCode, CmUnit::getUniPositionName));
        String positionCode = alarmEventDTO.getUniPositionCode();
        if (StringUtils.isNotBlank(positionCode) && uniPositionMap.containsKey(positionCode)) {
            String uniPositionName = uniPositionMap.get(positionCode);
            alarmEventDTO.setUniPositionName(uniPositionName);
        }
        map.put("alarmEvent", alarmEventDTO);
        String linkName = cmLinkDao.getLinkNameByCodes(alarmEventDTO.getLinkCodes());

        map.put("linkName", linkName);

        String deviceCodes = alarmEventDTO.getDeviceCodes();
        String linkCodes = alarmEventDTO.getLinkCodes();
        String appCodes = alarmEventDTO.getAppCodes();
        String eventWarnIds = alarmEventDTO.getEventWarnIds();
        // 涉及资产信息
        List<EventAsset> eventAssetList = new ArrayList<>();
        if (StringUtils.isNotBlank(deviceCodes)) {
            eventAssetList = deviceDao.getEventAssetList(deviceCodes, linkCodes, appCodes);
        }
        map.put("eventAssetList", eventAssetList);

        // 事件确认(深度解读) 查询关联的告警事件
        List<EventWarning> list = new ArrayList<>();
        if (StringUtils.isNotBlank(eventWarnIds)) {
            list = eventWarningDao.getEventWarnByIds(eventWarnIds);
        }
        map.put("eventWarnList", list);
        return map;
    }

    /**
     * 同一个链路上多个设备需要拆分多个安全事件；同一个链路多个设备是不同设备告警一种设备告警一条安全事件
     */
    @Override
    public void getEventWarn() {
        // 获取策略规则
        AlarmEventStrategyEntity dto = new AlarmEventStrategyEntity();
        dto.setRemarks("2");
        List<AlarmEventStrategyEntity> list = alarmEventStrategyDao.queryAlarmEventStrategyList(dto);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("安全策略为空");
            return;
        }

        List<CmDevLink> borderDeviceList = new ArrayList<>();
        if (Boolean.FALSE.equals(redisTemplate.hasKey(BORDER_ALARM_EVENT_KEY))) {
            log.error("redis中不存在key值:{}", BORDER_ALARM_EVENT_KEY);
            // // 获取边界设备 设备类型-前/后置机 网闸，单向光闸
            borderDeviceList = deviceDao.getBorderDeviceList("1005,1016,1017,1018");
            if (CollectionUtils.isEmpty(borderDeviceList)) {
                log.warn("安全事件分析-没有边界设备");
                return;
            }
            redisTemplate.opsForValue().set(BORDER_ALARM_EVENT_KEY, JSONArray.toJSONString(borderDeviceList));
        }

        String rdata = redisTemplate.opsForValue().get(BORDER_ALARM_EVENT_KEY);
        if (StringUtils.isBlank(rdata)) {
            log.error("redis中不存在key的value值为空");
            return;
        }
        Object parse = JSON.parse(rdata);
        List<CmDevLink> cmDevLinkList = JSON.parseArray(parse.toString(), CmDevLink.class);

        // 使用并行流进行操作
        Map<String, Map<String, CmDevLink>> borderCmDevMap = cmDevLinkList.parallelStream()
                // 过滤掉不需要的元素
                .filter(mapping -> mapping != null && StringUtils.isNotBlank(mapping.getDevId()))
                // 根据devId分组
                .collect(Collectors.groupingBy(
                        CmDevLink::getLinkId, // 分组键
                        LinkedHashMap::new, // 使用LinkedHashMap来保持插入顺序
                        Collectors.toMap(
                                CmDevLink::getDevId, // 键映射函数
                                Function.identity(), // 值映射函数
                                (existing, replacement) -> existing, // 合并函数，处理键值冲突
                                LinkedHashMap::new // 提供一个LinkedHashMap来保持插入顺序
                        )
                ));

        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


        // 获取 处置建议
        for (AlarmEventStrategyEntity entity : list) {
            AlarmEventStrategyConfig alarmEventStrategyConfig = JSON.parseObject(entity.getConfig(), AlarmEventStrategyConfig.class);
            if (Objects.isNull(alarmEventStrategyConfig)) {
                continue;
            }
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            int timeWindow = entity.getTimeWindow();
            String startime = getStartTime(now, timeWindow, formatter);
            String endTime = now.format(formatter);
            log.debug("getEventWarn function  startTime:{}-EndTime: {}", startime, endTime);
            getAlarmEventStrategyConfig(entity, alarmEventStrategyConfig, borderCmDevMap, startime, endTime);
        }

    }

    @Override
    public void getIllefalEbentWarn() {
        // 获取策略规则
        AlarmEventStrategyEntity dto = new AlarmEventStrategyEntity();
        dto.setRemarks("1");
        List<AlarmEventStrategyEntity> list = alarmEventStrategyDao.queryAlarmEventStrategyList(dto);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("安全策略为空");
            return;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (AlarmEventStrategyEntity entity : list) {
            AlarmEventStrategyConfig alarmEventStrategyConfig = JSON.parseObject(entity.getConfig(), AlarmEventStrategyConfig.class);
            if (Objects.isNull(alarmEventStrategyConfig)) {
                continue;
            }
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            int timeWindow = entity.getTimeWindow();
            String startime = getStartTime(now, timeWindow, formatter);
            String endTime = now.format(formatter);
            log.debug("getIllefalEbentWarn function  startTime:{}-EndTime: {}", startime, endTime);
            getIllefalAlarmEventStrategyConfig(entity, alarmEventStrategyConfig, startime, endTime);
        }

    }

    private void getIllefalAlarmEventStrategyConfig(AlarmEventStrategyEntity entity, AlarmEventStrategyConfig config, String starTime, String endTime) {
        List<EventWarning> linkEventWarnList = null;
        if (StringUtils.isBlank(config.getEventTypeMsg())) {
            log.warn("策略为空 -{}", config.getEvnetType());
            return;
        }
        String eventTypeMsg = config.getEventTypeMsg();
        linkEventWarnList = eventWarningDao.getBorderAlarmList(eventTypeMsg, starTime, endTime);
        if (CollectionUtils.isEmpty(linkEventWarnList)) {
            log.warn("链路告警为空");
            return;
        }

        //  违规未注册链路
        if ("1005".equals(eventTypeMsg)) {
            getUnregisteredLink(linkEventWarnList, eventTypeMsg, entity, starTime, endTime);
            return;
        }
        // 敏感数据出网
        if ("4001".equals(eventTypeMsg)) {
            getSensitiveApp(linkEventWarnList, eventTypeMsg, entity, starTime, endTime);
            return;
        }

        String deviceIp1 = defaultIfBlank(null);
        String deviceIp2 = defaultIfBlank(null);
        String devicePort1 = defaultIfBlank(null);
        String devicePort2 = defaultIfBlank(null);
        for (EventWarning warning : linkEventWarnList) {
            // 获取链路信息
            CmDevLink cmDevLink = cmLinkDao.getlinkInfo(warning.getLinkId());
            if (Objects.isNull(cmDevLink)) {
                log.warn("安全事件分析-链路不存在");
                continue;
            }
            // 获取告警中的ip,端口
            List<String[]> ipPortPairs = getWarnIp(warning.getRemark());
            if (ipPortPairs.isEmpty()) {
                log.warn("该【{}-{}】告警未匹配到相关内容", warning.getEventType(), warning.getRemark());
                continue;
            }
            if (ipPortPairs.size() > 0) {
                deviceIp1 = ipPortPairs.get(0)[0];
                devicePort1 = ipPortPairs.get(0)[1];
            }
            if (ipPortPairs.size() > 1) {
                deviceIp2 = ipPortPairs.get(1)[0];
                devicePort2 = ipPortPairs.get(1)[1];
            }
            // 生成安全事件
            String strategyName = entity.getStrategyName(); // 事件名称
            String eventDescTemplate = entity.getEventDescTemplate(); // 事件描述模板
            String eventExplainTemplate = entity.getEventExplainTemplate(); // 事件解读还原模板
            String handlingSuggestionTemplate = entity.getHandlingSuggestionTemplate(); //处置建议模板

            String uniPositionName = defaultIfBlank(cmDevLink.getUniPositionName());
            String policeName = defaultIfBlank(cmDevLink.getPoliceName()); //责任民警姓名
            String manufacturerPerson = defaultIfBlank(cmDevLink.getManufacturerPerson()); //承建厂商名称
            String positionCode = defaultIfBlank(cmDevLink.getUniPositionCode()); //站点
            String domainName = defaultIfBlank(cmDevLink.getDomainName());//网域名称
            String linkCode = defaultIfBlank(cmDevLink.getLinkCode());//链路标识
            String linkName = defaultIfBlank(cmDevLink.getLinkName());


            JSONObject handlingSuggestionJson = entity.getHandlingSuggestionJson();
            String base = handlingSuggestionJson.getString("base");
            Integer linkEventType = warning.getEventType();
            // 替换模板中的占位符
            // 处置建议
            handlingSuggestionTemplate = getEventDescTemplate(null, linkEventType, handlingSuggestionJson);
            handlingSuggestionTemplate = base + handlingSuggestionTemplate;
            handlingSuggestionTemplate = replaceTemplate(handlingSuggestionTemplate, "manufacturerPerson", manufacturerPerson, "policeName", policeName, "deviceIp1", deviceIp1, "deviceIp2", deviceIp2, "devicePort1", devicePort1, "devicePort2", devicePort2);

            // 事件描述
            JSONObject descJson = entity.getHeventDescJson();
            eventDescTemplate = getEventDescTemplate(null, linkEventType, descJson);
            eventDescTemplate = replaceTemplate(eventDescTemplate, "linkAlarmTime", warning.getAlarmTime(), "uniPositionName", uniPositionName, "linkName", linkName, "domainName", domainName, "deviceIp1", deviceIp1, "devicePort1", devicePort1);


            // 事件解读还原
            JSONObject eventExplainTemplateJson = entity.getEventExplainTemplateJson();
            eventExplainTemplate = getEventDescTemplate(null, linkEventType, eventExplainTemplateJson);
            eventExplainTemplate = replaceTemplate(eventExplainTemplate, "uniPositionName", uniPositionName, "domainName", domainName, "linkName", linkName, "linkAlarmTime", warning.getAlarmTime(), "deviceIp1", deviceIp1, "deviceIp2", deviceIp2, "devicePort1", devicePort1, "devicePort2", devicePort2);
            saveAlarmEvent(entity, strategyName, eventDescTemplate, eventExplainTemplate, handlingSuggestionTemplate, positionCode, domainName, linkCode, null, warning.getId(), null);
        }
    }

    private Map<String, List<EventWarning>> getEventObjectIdMap(List<EventWarning> list) {
        // 根据getEventObjectId分组，直接得到Map<String, List<EventWarning>>
        Map<String, List<EventWarning>> groupedByEventObjectId = list.stream()
                .filter(eventWarning -> eventWarning.getEventObjectId() != null && !eventWarning.getEventObjectId().isEmpty())
                .sorted(Comparator.comparing(EventWarning::getAlarmTime))
                .collect(Collectors.groupingBy(EventWarning::getEventObjectId));

        return groupedByEventObjectId;
    }

    /**
     * 安全事件-敏感数据出网
     *
     * @param appEventWarnList
     * @param appType
     * @param entity
     */
    private void getSensitiveApp(List<EventWarning> appEventWarnList, String appType, AlarmEventStrategyEntity entity, String starTime, String endTime) {
        // 生成安全事件
        Integer eventType = Integer.valueOf(appType);
        String strategyName = entity.getStrategyName(); // 事件名称
        String eventDescTemplate = entity.getEventDescTemplate(); // 事件描述模板
        String eventExplainTemplate = entity.getEventExplainTemplate(); // 事件解读还原模板
        String handlingSuggestionTemplate = entity.getHandlingSuggestionTemplate(); //处置建议模板
        JSONObject handlingSuggestionJson = entity.getHandlingSuggestionJson();
        String base = handlingSuggestionJson.getString("base");
        JSONObject eventExplainTemplateJson = entity.getEventExplainTemplateJson();
        // 根据业务进行分组
        Map<String, List<EventWarning>> eventObjectIdMap = getEventObjectIdMap(appEventWarnList);
        for (Map.Entry<String, List<EventWarning>> entry : eventObjectIdMap.entrySet()) {
            String appId = entry.getKey();
            // 获取业务名称，链路名称，网域名称，站点，业务联系人
            SadApp sadApp = appDao.getAppInfoById(appId);
            if (Objects.isNull(sadApp)) {
                log.debug("业务id:{}不存在", appId);
                continue;
            }
            List<EventWarning> eventWarningList = entry.getValue();
            String appName = defaultIfBlank(sadApp.getSystemName());
            String linkName = defaultIfBlank(sadApp.getLink_name());
            String uniPositionName = defaultIfBlank(sadApp.getUniPositionName());
            String uniPositionCode = defaultIfBlank(sadApp.getUniPositionCode());
            String competentName = defaultIfBlank(sadApp.getCompetentName());
            String competentPhone = defaultIfBlank(sadApp.getCompetentPhone());
            String domainName = defaultIfBlank(sadApp.getDomainName());
            String domainId = defaultIfBlank(sadApp.getDomainId());
            String linkCode = defaultIfBlank(sadApp.getLinkCode());
            String appCode = defaultIfBlank(sadApp.getAppCode());
            // 告警eventIds
            String eventIds = getEventAlarmId(eventWarningList);

            // 根据业务id关联t_sensitivedata敏感表appName(id)
            List<Sensitivedata> sensitivedataList = sensitivedataDao.selectSensitiveList(appId, starTime, endTime);
            if (CollectionUtils.isEmpty(sensitivedataList)) {
                log.debug("业务id:{},在{}-{}时间范围内没有敏感数据", appId, starTime, endTime);
                continue;
            }
            // 替换模板中的占位符
            // 处置建议
            handlingSuggestionTemplate = getEventDescTemplate(null, eventType, handlingSuggestionJson);
            handlingSuggestionTemplate = base + handlingSuggestionTemplate;
            handlingSuggestionTemplate = replaceTemplate(handlingSuggestionTemplate, "name", competentName, "phone", competentPhone);

            // 事件描述
            JSONObject descJson = entity.getHeventDescJson();
            eventDescTemplate = getEventDescTemplate(null, eventType, descJson);
            eventDescTemplate = replaceTemplate(eventDescTemplate, "firstAlarmTime",
                    eventWarningList.get(0).getAlarmTime(), "uniPositionName", uniPositionName,
                    "linkName", linkName, "domainName", domainName, "appName", appName, "alarmCount", String.valueOf(eventWarningList.size()));

            // 事件解读还原
            eventExplainTemplate = eventExplainTemplateJson.getString("base");
            eventExplainTemplate = replaceTemplate(eventExplainTemplate, "uniPositionName", uniPositionName, "domainName", domainName, "linkName", linkName, "appName", appName);

            // 根据srcIp,dstIP分组
            Map<String, List<Sensitivedata>> groupedByIp = sensitivedataList.stream()
                    .collect(Collectors.groupingBy(data -> data.getSrc_ip() + ":" + data.getDst_ip()));
            String transMethod = "--";
            StringBuilder fielNames = new StringBuilder();
            StringBuilder finalSensitive = new StringBuilder(eventExplainTemplate);
            for (Map.Entry<String, List<Sensitivedata>> stringListEntry : groupedByIp.entrySet()) {
                String[] ipPair = stringListEntry.getKey().split(":");
                String srcIp = ipPair[0];
                String dstIp = ipPair[1];
                List<Sensitivedata> dataList = stringListEntry.getValue();

                StringBuilder sensitiveBuilder = new StringBuilder();
                sensitiveBuilder.append("泄露源IP：").append(srcIp).append("，目标IP：").append(dstIp).append("\n");

                for (Sensitivedata data : dataList) { //时间
                    transMethod = data.getTransMethod();
                    // 去告警表根据时间范围,告警类型，业务id,remark like fielName, violatContent,主要获取业务同一个文件中最新告警时间
                    EventWarning eventWarning = eventWarningDao.getAppEventWarn(eventType, starTime, endTime, appId);
                    if(Objects.isNull(eventWarning)){
                        continue;
                    }
                    String alarmTime = defaultIfBlank(eventWarning.getAlarmTime());
                    sensitiveBuilder.append("【").append(alarmTime).append("】起，文件名为【")
                            .append(data.getFileName()).append("】发生数据泄露，涉及")
                            .append(data.getViolatContent()).append("等敏感内容。\n");
                    if (fielNames != null && fielNames.toString().contains(data.getFileName())) {
                        continue;
                    }
                    fielNames.append("【").append(data.getFileName()).append("】").append("、");
                }

                finalSensitive.append(sensitiveBuilder);
            }
            fielNames.deleteCharAt(fielNames.length() - 1);
            eventExplainTemplate = replaceTemplate(finalSensitive.toString(), "transMethod", transMethod);
            handlingSuggestionTemplate = replaceTemplate(handlingSuggestionTemplate, "fileNames", fielNames.toString());

            saveAlarmEvent(entity, strategyName, eventDescTemplate, eventExplainTemplate, handlingSuggestionTemplate, uniPositionCode, domainName, linkCode, null, eventIds, appCode);
        }
    }

    /**
     * 未注册链路安全事件
     *
     * @param linkEventType
     */
    private void getUnregisteredLink(List<EventWarning> linkEventWarnList, String linkEventType, AlarmEventStrategyEntity entity, String starTime, String endTime) {

        // 生成安全事件
        String strategyName = entity.getStrategyName(); // 事件名称
        String eventDescTemplate = entity.getEventDescTemplate(); // 事件描述模板
        String eventExplainTemplate = entity.getEventExplainTemplate(); // 事件解读还原模板
        String handlingSuggestionTemplate = entity.getHandlingSuggestionTemplate(); //处置建议模板

        List<BorderTask> taskList = taskResultDao.getBorderTaskList(starTime, endTime);
        if (CollectionUtils.isEmpty(taskList)) {
            log.debug("【{}-{}】时间范围内没有扫描完成的任务", starTime, endTime);
            return;
        }
        // 替换模板中的占位符
        // 处置建议
        JSONObject handlingSuggestionJson = entity.getHandlingSuggestionJson();
        String handlingSuggestionBase = handlingSuggestionJson.getString("base");
        handlingSuggestionTemplate = getEventDescTemplate(null, Integer.valueOf(linkEventType), handlingSuggestionJson);

        // 事件描述
        JSONObject descJson = entity.getHeventDescJson();
        eventDescTemplate = getEventDescTemplate(null, Integer.valueOf(linkEventType), descJson);


        // 事件解读还原
        JSONObject eventExplainTemplateJson = entity.getEventExplainTemplateJson();
        eventExplainTemplate = getEventDescTemplate(null, Integer.valueOf(linkEventType), eventExplainTemplateJson);
        String eventExplainBase = eventExplainTemplateJson.getString("base");
        for (BorderTask task : taskList) {
            String alarmId = task.getAlarmId();  // 任务关联的未注册链路告警ids
            if (StringUtils.isBlank(alarmId)) {
                log.debug("【{}】任务没有未注册链路告警", task.getName());
                continue;
            }
            handlingSuggestionTemplate = getEventDescTemplate(null, Integer.valueOf(linkEventType), handlingSuggestionJson);
            eventDescTemplate = getEventDescTemplate(null, Integer.valueOf(linkEventType), descJson);
            String taskStartTime = defaultIfBlank(task.getStartTime());// 开始时间
            String taskEndTime = defaultIfBlank(task.getEndTime()); //结束时间
            String uniPositionCode = defaultIfBlank(task.getUniPositionCode()); // 站点
            String name = defaultIfBlank(task.getName()); //任务名称
            String activeIpCount = defaultIfBlank(String.valueOf(task.getActiveIpCount())); // 存活IP数
            String ipTotal = defaultIfBlank(String.valueOf(task.getIpTotal())); // 总IP数
            String destination = defaultIfBlank(task.getDestination());// 探测目标 ip段
            String uniPositionName = defaultIfBlank(null);
            // 站点名称
            BigDecimal activeRate = task.getActiveRate();// 网络可达率

            // 获取站点名称
            CmUnit cmUnit = iCmUtilDao.getCmUnit(uniPositionCode);
            if (Objects.nonNull(cmUnit) && StringUtils.isNotBlank(cmUnit.getUniPositionName())) {
                uniPositionName = cmUnit.getUniPositionName();
            }

            // 根据未注册链路资产id查询资产集合
            String propertyId = task.getPropertyId();
            List<UnregisteredProperty> unregisteredPropertyList = taskResultDao.getPropertyList(propertyId);
            if (CollectionUtils.isEmpty(unregisteredPropertyList)) {
                log.debug(task.getName() + "任务在【{}】-【{}】时间范围内没有未注册链路", starTime, endTime);
                continue;
            }
            // 未注册资产数量
            int count = unregisteredPropertyList.size();

            // 事件描述
            eventDescTemplate = replaceTemplate(eventDescTemplate, "taskStartTime", taskStartTime, "uniPositionName", uniPositionName, "taskStopTime", taskEndTime, "linkCount", String.valueOf(count));

            // 事件解读还原
          String  eventExplain = replaceTemplate(eventExplainTemplate, "uniPositionName", uniPositionName,
                    "taskStartTime", taskStartTime, "taskName", name, "taskStopTime", taskEndTime,
                    "borderIp", destination, "ipCount", ipTotal, "accessibleIpCount", activeIpCount,
                    "achievableRate", String.valueOf(activeRate), "linkCount", String.valueOf(count));
            StringBuilder stringBuilder = new StringBuilder();
            HashMap<String, String> manufacturerMap = new HashMap<>();
            for (UnregisteredProperty property : unregisteredPropertyList) {
                String template = eventExplainBase
                        .replace("alarmTime", defaultIfBlank(property.getCreateTime()))
                        .replace("borderIp", defaultIfBlank(property.getIpAddress()))
                        .replace("borderPort", defaultIfBlank(property.getIpPort()))
                        .replace("manufacturerName", defaultIfBlank(property.getManufacturer()))
                        .replace("deviceType", property.getAssetType());
                stringBuilder.append(template);

                // 拼接处置建议数据
                String deviceTypeIpValue = "【" + property.getAssetType() + "】" + "（" + property.getIpAddress() + "）";

                // 检查HashMap中是否已经有了当前manufacturer的键
                if (manufacturerMap.containsKey(property.getManufacturer())) {
                    // 如果已经有了，将新的deviceType（ip）拼接到已有的值后面
                    String existingValue = manufacturerMap.get(property.getManufacturer());
                    manufacturerMap.put(property.getManufacturer(), existingValue + "、" + deviceTypeIpValue);
                } else {
                    // 如果没有，直接将deviceType（ip）作为值放入HashMap
                    manufacturerMap.put(property.getManufacturer(), deviceTypeIpValue);
                }
            }
            eventExplain = eventExplain + stringBuilder;

            // 处置建议
            StringBuilder handlingSuggestionStringBuilder = new StringBuilder();
            for (Map.Entry<String, String> entry : manufacturerMap.entrySet()) {
                String template = handlingSuggestionTemplate
                        .replace("manufacturerName", entry.getKey())
                        .replace("assetTypeAndIps", entry.getValue());
                handlingSuggestionStringBuilder.append(template);
            }
            handlingSuggestionTemplate = handlingSuggestionBase + handlingSuggestionStringBuilder;

            saveAlarmEvent(entity, strategyName, eventDescTemplate, eventExplain, handlingSuggestionTemplate, uniPositionCode, null, null, null, alarmId, null);
        }
    }


    private List<String[]> getWarnIp(String remark) {
        // 创建一个HashMap来存储IP和端口的对应关系
        HashMap<String, Integer> hashMap = new HashMap<>();

        // 使用正则表达式匹配IP地址和端口号
        String ipPattern = "(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})的(\\d{2,5})端口";
        //String ipPattern = "\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b.*?\\b(\\d+)\\b";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(ipPattern);
        java.util.regex.Matcher matcher = pattern.matcher(remark);

        List<String[]> ipPortPairs = new ArrayList<>();

        // 将匹配到的IP和端口放入列表
        while (matcher.find()) {
            ipPortPairs.add(new String[]{matcher.group(1), matcher.group(2)});
        }
        return ipPortPairs;
}

    private void getAlarmEventStrategyConfig(AlarmEventStrategyEntity entity, AlarmEventStrategyConfig config, Map<String, Map<String, CmDevLink>> borderCmDevMap, String starTime, String endTime) {

        JSONObject jsonObject = new JSONObject();
        List<EventWarning> deviceEventWarnList = null;
        if (StringUtils.isBlank(config.getEventTypeMsg())) {
            log.warn("策略为空 -{}", config.getEvnetType());
            return;
        }
        String eventTypeMsg = config.getEventTypeMsg();
        deviceEventWarnList = eventWarningDao.getBorderAlarmList(eventTypeMsg, starTime, endTime);
        if (CollectionUtils.isEmpty(deviceEventWarnList)) {
            log.warn("设备告警为空");
            return;
        }


        // 分组操作
        Map<String, List<EventWarning>> groupedByLinkId = deviceEventWarnList.stream()
                .filter(eventWarning -> eventWarning.getLinkId() != null && !eventWarning.getLinkId().isEmpty())
                .collect(Collectors.groupingBy(EventWarning::getLinkId));

        Map<String, List<EventWarning>> matchedGroups = checkDevicesExist(eventTypeMsg, groupedByLinkId);
        if (matchedGroups.isEmpty()) {
            log.warn("安全事件分析-不满足策略条件:::{}", groupedByLinkId);
            return;
        }
        // 输出匹配到的组
        matchedGroups.forEach((linkId, eventWarnings) -> {
            Map<String, CmDevLink> cmDevLinkMap = new HashMap<>();
            if (borderCmDevMap.containsKey(linkId)) {
                // 边界设备-链路-多个业务id
                cmDevLinkMap = borderCmDevMap.get(linkId);
                Map<Integer, List<EventWarning>> groupedByEventType = eventWarnings.stream()
                        .collect(Collectors.groupingBy(EventWarning::getEventType));
                List<EventWarning> deviceList = new ArrayList<>();
                List<EventWarning> linkList = new ArrayList<>();
                List<EventWarning> appList = new ArrayList<>();
                groupedByEventType.forEach((eventType, warnings) -> {
                    if (String.valueOf(eventType).startsWith("3")) {
                        // 如果eventType以3开头，检查是否匹配cmDevLink的devId
                        deviceList.addAll(warnings);
                    }
                    if (String.valueOf(eventType).startsWith("1")) {
                        // 如果eventType以3开头，检查是否匹配cmDevLink的devId
                        linkList.addAll(warnings);
                    }
                    if (String.valueOf(eventType).startsWith("2")) {
                        // 如果eventType以3开头，检查是否匹配cmDevLink的devId
                        appList.addAll(warnings);
                    }
                });
                for (EventWarning warning : deviceList) {
                    String eventObjectId = warning.getEventObjectId();
                    if (StringUtils.isBlank(eventObjectId) || !cmDevLinkMap.containsKey(eventObjectId)) {
                        continue;
                    }
                    CmDevLink cmDevLink = cmDevLinkMap.get(eventObjectId);
                    // 生成安全事件
                    jsonObject.put("deviceType", warning.getEventType());
                    jsonObject.put("appIds", getEventObjectId(appList));
                    jsonObject.put("cmLink", cmDevLink);
                    jsonObject.put("linkAlarm", linkList.get(0));
                    jsonObject.put("deviceAlarm", warning);
                    jsonObject.put("eventIds", getEventAlarmId(eventWarnings));
                    jsonObject.put("appAlarmList", appList);
                    jsonObject.put("startTime", starTime);
                    jsonObject.put("endTime", endTime);
                    // 处置建议，事件还原，描述
                    getHandlingSuggestion(entity, jsonObject);
                }
            }
        });

    }

    private String replaceTemplate(String template, String... replacements) {
        if (replacements.length % 2 != 0) {
            throw new IllegalArgumentException("Replacement arguments must be in pairs.");
        }
        for (int i = 0; i < replacements.length; i += 2) {
            String placeholder = replacements[i];
            String value = replacements[i + 1];
            // 使用Matcher.quoteReplacement来避免转义字符的问题
            template = template.replaceAll(Pattern.quote(placeholder), Matcher.quoteReplacement(value));
        }
        return template;
    }

    private String getEventObjectId(List<EventWarning> list) {
        String appIdsSplit = list.stream()
                .collect(Collectors.groupingBy(
                        EventWarning::getEventObjectId, // 分组的键
                        Collectors.mapping(
                                EventWarning::getEventObjectId, // 映射到appId
                                Collectors.collectingAndThen(
                                        Collectors.toSet(), // 去重
                                        appIds -> String.join(",", appIds) // 拼接appId
                                )
                        )
                ))
                .values() // 获取所有分组后的appId字符串
                .stream() // 将这些字符串转换成一个流
                .collect(Collectors.joining(",")); // 使用分号分隔每个分组的结果*/
        return appIdsSplit;
    }

    private List<String> getEventLinkId(List<EventWarning> list) {
        List<String> eventObjectIds = list.stream()
                .filter(eventWarning -> eventWarning.getLinkId() != null && !eventWarning.getLinkId().isEmpty())
                .map(EventWarning::getLinkId)
                .collect(Collectors.toList());
        return eventObjectIds;
    }

    private String getEventAlarmId(List<EventWarning> list) {
        String eventObjectIds = list.stream()
                .filter(eventWarning -> eventWarning.getId() != null && !eventWarning.getId().isEmpty())
                .map(EventWarning::getId)
                .collect(Collectors.joining(","));
        return eventObjectIds;
    }

    private String getEventAlarmBreakId(List<EventWarning> list, String eventType) {
        String eventObjectIds = list.stream()
                .filter(eventWarning -> eventWarning.getId() != null && !eventWarning.getId().isEmpty() && String.valueOf(eventWarning.getEventType()).equals(eventType))
                .map(EventWarning::getId)
                .collect(Collectors.joining(","));
        return eventObjectIds;
    }

    /**
     * 处置建议，事件还原，描述
     *
     * @param entity
     */
    private void getHandlingSuggestion(AlarmEventStrategyEntity entity, JSONObject jsonObject) {
        if (jsonObject.isEmpty()) {
            log.warn("getHandlingSuggestion-jsonObject:::{}", jsonObject);
            return;
        }
        //获取设备所在链路的承建厂商责任人，责任民警姓名
        CmDevLink cmDevLink = JSON.parseObject(JSON.toJSONString(jsonObject.get("cmLink")), CmDevLink.class);
        if (Objects.isNull(cmDevLink)) {
            return;
        }
        String strategyName = entity.getStrategyName(); // 事件名称
        String eventDescTemplate = entity.getEventDescTemplate(); // 事件描述模板
        String eventExplainTemplate = entity.getEventExplainTemplate(); // 事件解读还原模板
        String handlingSuggestionTemplate = entity.getHandlingSuggestionTemplate(); //处置建议模板

        String uniPositionName = defaultIfBlank(cmDevLink.getUniPositionName());
        String policeName = defaultIfBlank(cmDevLink.getPoliceName()); //责任民警姓名
        String manufacturerPerson = defaultIfBlank(cmDevLink.getManufacturerPerson()); //承建厂商名称
        String positionCode = defaultIfBlank(cmDevLink.getUniPositionCode()); //站点
        String domainName = defaultIfBlank(cmDevLink.getDomainName());//网域名称
        String linkCode = defaultIfBlank(cmDevLink.getLinkCode());//链路标识
        String deviceCode = defaultIfBlank(cmDevLink.getDeviceCode());//设备标识
        String linkName = defaultIfBlank(cmDevLink.getLinkName());
        String deviceType = defaultIfBlank(cmDevLink.getDeviceType());
        String devIp = defaultIfBlank(cmDevLink.getDevIp());

        EventWarning warning = new EventWarning();
        if (jsonObject.containsKey("deviceAlarm")) {
            warning = (EventWarning) jsonObject.get("deviceAlarm");
        }
        EventWarning linkWarning = new EventWarning();
        if (jsonObject.containsKey("linkAlarm")) {
            linkWarning = (EventWarning) jsonObject.get("linkAlarm");
        }
        if (Objects.isNull(warning)) {
            log.warn("安全事件-设备告警为空");
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(warning.getId()).append(",").append(linkWarning.getId()).append(",");
        String deviceRemark = warning.getRemark();
        String deviceWarnIp = getWarnDeviceIp(deviceRemark);
        // 获取设备类型-模型名称
        String deviceModelName = getDeviceModelName(warning.getEventType());
        deviceModelName = defaultIfBlank(deviceModelName);


        String eventIds = null;
        if (jsonObject.containsKey("eventIds")) {
            eventIds = jsonObject.getString("eventIds");
        }
        JSONObject handlingSuggestionJson = entity.getHandlingSuggestionJson();
        String base = handlingSuggestionJson.getString("base");
        Integer deviceEventType = warning.getEventType();

        // 替换模板中的占位符
        // 处置建议
        handlingSuggestionTemplate = getEventDescTemplate(deviceRemark, deviceEventType, handlingSuggestionJson);
        handlingSuggestionTemplate = base + handlingSuggestionTemplate;
        handlingSuggestionTemplate = replaceTemplate(handlingSuggestionTemplate,
                "manufacturerPerson", manufacturerPerson,
                "policeName", policeName,
                "deviceType", deviceType,
                "deviceIp", deviceWarnIp,
                "deviceAlarmTime", warning.getAlarmTime());
        if (handlingSuggestionTemplate.contains("processName")) {
            String processName = cmLinkDao.getProcessName(deviceWarnIp);
            handlingSuggestionTemplate = replaceTemplate(handlingSuggestionTemplate,
                    "processName", defaultIfBlank(processName));//进程异常-进程名称
        }

        // 事件描述
        JSONObject descJson = entity.getHeventDescJson();
        eventDescTemplate = getEventDescTemplate(deviceRemark, deviceEventType, descJson);
        eventDescTemplate = replaceTemplate(eventDescTemplate,
                "deviceAlarmTime", warning.getAlarmTime(),
                "uniPositionName", uniPositionName,
                "linkName", linkName,
                "domainName", domainName,
                "deviceType", deviceType,
                "deviceIp", deviceWarnIp);


        // 事件解读还原
        JSONObject eventExplainTemplateJson = entity.getEventExplainTemplateJson();
        eventExplainTemplate = getEventDescTemplate(null, deviceEventType, eventExplainTemplateJson);
        eventExplainTemplate = replaceTemplate(eventExplainTemplate,
                "uniPositionName", uniPositionName,
                "domainName", domainName,
                "linkName", linkName,
                "deviceAlarmTime", warning.getAlarmTime(),
                "deviceType", deviceType,
                "deviceIp", deviceWarnIp,
                "deviceModelName", deviceModelName,
                "linkAlarmTime", linkWarning.getAlarmTime());

        // 业务告警集合
        JSONArray results1 = jsonObject.getJSONArray("appAlarmList");
        List<EventWarning> appAlarmList = results1.toJavaList(EventWarning.class);


        // 3001硬件故障时  业务类告警只有中断时，原来逻辑。否则多个业务类型告警时事件还原只展示典型业务还原情况
        if (jsonObject.getString("deviceType").equals("3001")) {
            eventExplainTemplate = replaceTemplate(eventExplainTemplate,
                    "deviceAlarmTime", warning.getAlarmTime(),
                    "deviceType", deviceType,
                    "deviceIp", deviceWarnIp);
            eventExplainTemplate = getALarmeventExplain(deviceCode, appAlarmList, eventExplainTemplate, eventExplainTemplateJson,
                    jsonObject.getString("startTime"), jsonObject.getString("endTime"), eventDescTemplate,
                    handlingSuggestionTemplate, entity, eventIds, warning.getAlarmTime(), deviceType, deviceWarnIp);
        }
        if (StringUtils.isBlank(eventExplainTemplate)) {
            return;
        }
        String appAlarmIds = getEventAlarmBreakId(appAlarmList, "2001");
        stringBuilder.append(appAlarmIds);
        // 获取业务信息  appCodes, name,ip,port
        List<SadApp> appList = new ArrayList<>();
        if (jsonObject.containsKey("appIds")) {
            String appIds = jsonObject.getString("appIds");
            appList = appDao.getBorderAppList(appIds);
        }

        if (CollectionUtils.isEmpty(appList) && CollectionUtils.isEmpty(appAlarmList)) {
            return;
        }
        appList = getAppList(appAlarmList, appList);
        StringBuilder appCodes = new StringBuilder();
        StringBuilder appNames = new StringBuilder(); // 用于存储额外的appName
        int placeholderIndex = 1; // 初始化占位符索引
        String firstAlarmTime = "--";
        for (SadApp sadApp : appList) {
            // 获取首次 告警时间
            String alarmTime = sadApp.getAlarmTime();
            if (placeholderIndex == 1) {
                firstAlarmTime = alarmTime;
            }
            String appCode = sadApp.getAppCode();
            String appIp = sadApp.getAppIp();
            String systemName = sadApp.getSystemName();
            String appPort = sadApp.getAppPort();
            appCodes.append(appCode);
            if (placeholderIndex > 3) {
                // 如果已经拼接了三个业务，将额外的appName加入StringBuilder
                if (appNames.length() > 0) {
                    appNames.append("、");
                }
                appNames.append(sadApp.getSystemName());
            } else {
                // 使用generatePlaceholder方法生成动态占位符
                String appNamePlaceholder = generatePlaceholder("appName", placeholderIndex);
                String appIpPlaceholder = generatePlaceholder("appIp", placeholderIndex);
                String appPortPlaceholder = generatePlaceholder("appPort", placeholderIndex);
                String appAlarmTimePlaceholder = generatePlaceholder("appAlarmTime", placeholderIndex);
                eventExplainTemplate = replaceTemplate(eventExplainTemplate,
                        appNamePlaceholder, systemName,
                        appIpPlaceholder, appIp,
                        appPortPlaceholder, appPort,
                        appAlarmTimePlaceholder, alarmTime);
            }
            placeholderIndex++; // 更新占位符索引
            appCodes.append(",");
        }
        appCodes.deleteCharAt(appCodes.length() - 1);
        // 如果存在额外的appName，替换模板中的appNames占位符
        if (appNames.length() > 0) {
            eventExplainTemplate = replaceTemplate(eventExplainTemplate, "appNames", appNames.toString());
        } else {
            // 截取后面多余
            eventExplainTemplate = substringBeforeThirdSemicolon(eventExplainTemplate, placeholderIndex);
        }
        eventDescTemplate = replaceTemplate(eventDescTemplate,
                "appAlarmTime", firstAlarmTime);
        saveAlarmEvent(entity, strategyName, eventDescTemplate, eventExplainTemplate, handlingSuggestionTemplate, positionCode, domainName, linkCode, deviceCode, stringBuilder.toString(), appCodes.toString());
    }

    private String getALarmeventExplain(String deviceCode, List<EventWarning> appAlarmList, String eventExplainTemplate, JSONObject eventExplainTemplateJson,
                               String startTime, String endTime, String eventDescTemplate, String handlingSuggestionTemplate,
                               AlarmEventStrategyEntity entity, String eventIds, String deviceAlarmTime, String deviceType, String deviceWarnIp) {
        // 用于存储包含2001告警的业务ID
        List<String> validBusinessIds = new ArrayList<>();
        // 用于存储每个业务的告警数量
        Map<String, Integer> alarmCountMap = new HashMap<>();
        StringBuilder appIds = new StringBuilder();
        StringBuilder eventTypes = new StringBuilder();


        appAlarmList = appAlarmList.stream()
                .sorted((e1, e2) -> Integer.compare(e1.getEventType(), e2.getEventType()))
                .collect(Collectors.toList());

        // 遍历告警列表，找出包含2001告警的业务
        for (EventWarning warning : appAlarmList) {
            if (warning.getEventType() == 2001) {
                if (!validBusinessIds.contains(warning.getEventObjectId())) {
                    eventTypes.append(warning.getEventType()).append(",");
                    validBusinessIds.add(warning.getEventObjectId());
                    alarmCountMap.put(warning.getEventObjectId(), alarmCountMap.getOrDefault(warning.getEventObjectId(), 0) + 1);
                }
                continue;
            }
            if (validBusinessIds.contains(warning.getEventObjectId())) {
                eventTypes.append(warning.getEventType()).append(",");
                alarmCountMap.put(warning.getEventObjectId(), alarmCountMap.get(warning.getEventObjectId()) + 1);
                continue;
            }
            // 移除业务id不满足有中断的告警id
            eventIds = eventIds.replaceAll(warning.getId() + ",", "").replaceAll("," + warning.getId(), "");
        }
        eventTypes.deleteCharAt(eventTypes.length() - 1);
        // 找出告警数量最多的业务
        String maxAlarmBusiness = null;
        int maxAlarmCount = 1;
        for (Map.Entry<String, Integer> entry : alarmCountMap.entrySet()) {
            if (entry.getValue() > maxAlarmCount) {
                maxAlarmBusiness = entry.getKey();
                maxAlarmCount = entry.getValue();
                continue;
            }
            appIds.append(entry.getKey()).append(",");
        }

        // maxAlarmBusines是null并且maxAlarmCount没有大于1的，说明业务层面告警只有业务中断，走原来【事件描述】模版，否则新模版
        if (StringUtils.isBlank(maxAlarmBusiness) && maxAlarmCount == 1) {
            return eventExplainTemplate;
        }
        // 典型业务
        SadApp sadApp = appDao.getAppInfoById(maxAlarmBusiness);
        eventExplainTemplate = eventExplainTemplateJson.getString("base");
        eventExplainTemplate = replaceTemplate(eventExplainTemplate,
                "uniPositionName", defaultIfBlank(sadApp.getUniPositionName()),
                "domainName", defaultIfBlank(sadApp.getDomainName()),
                "linkName", defaultIfBlank(sadApp.getLink_name()),
                "deviceAlarmTime", deviceAlarmTime,
                "deviceType", deviceType,
                "deviceIp", deviceWarnIp,
                "systemName", defaultIfBlank(sadApp.getSystemName()),
                "appIp", defaultIfBlank(sadApp.getAppIp()),
                "appPort", defaultIfBlank(sadApp.getAppPort()));
        List<EventWarning> warnings = eventWarningDao.getEventWarn(maxAlarmBusiness, sadApp.getUniPositionCode(), eventTypes.toString(), startTime, endTime);
        if (CollectionUtils.isEmpty(warnings)) {
            return eventExplainTemplate;
        }
        // 获取appCodes
        String appCodes = appDao.getAppCodeByIds(validBusinessIds);

        String firstAlarmTime = "--";
        int size = warnings.size();
        int placeholderIndex = 1; // 初始化占位符索引
        for (EventWarning warning : warnings) {
            // 获取首次 告警时间
            String alarmTime = warning.getAlarmTime();
            if (placeholderIndex == 1) {
                firstAlarmTime = alarmTime;
            }
            // 使用generatePlaceholder方法生成动态占位符
            String appPortPlaceholder = generatePlaceholder("appTypeName", placeholderIndex);
            String appAlarmTimePlaceholder = generatePlaceholder("appAlarmTime", placeholderIndex);
            eventExplainTemplate = replaceTemplate(eventExplainTemplate,
                    appPortPlaceholder, warning.getEventClassify(),
                    appAlarmTimePlaceholder, warning.getAlarmTime());
            placeholderIndex++; // 更新占位符索引
        }
        // 如果warnings集合的大小小于4，删除多余的占位符行
        if (size < 4 && placeholderIndex == 4) {
            // 替换包含【还触发了】的行
            eventExplainTemplate = eventExplainTemplate.replaceAll("(?m)^.*还触发了.*\\n?", "");
        } else {
            // 替换包含【紧接着出现】的行
            eventExplainTemplate = eventExplainTemplate.replaceAll("(?m)^.*紧接着出现.*\\n?", "");
            // 替换包含【还触发了】的行
            eventExplainTemplate = eventExplainTemplate.replaceAll("(?m)^.*还触发了.*\\n?", "");
        }

        // 确保最后一条数据总是赋值给【appAlarmTime4】：最终触发了【appTypeName4】告警】
        if (size > 0) {
            EventWarning lastWarning = warnings.get(size - 1);
            eventExplainTemplate = eventExplainTemplate.replace("appTypeName4", lastWarning.getEventClassify());
            eventExplainTemplate = eventExplainTemplate.replace("appAlarmTime4", lastWarning.getAlarmTime());
        }


        // 如果存在额外的appName，替换模板中的appNames占位符
        if (appIds.length() > 0) {
            appIds.deleteCharAt(appIds.length() - 1);
            String appNames = appDao.getAppNamesByIds(appIds.toString());
            eventExplainTemplate = replaceTemplate(eventExplainTemplate, "appNames", appNames);
        } else {
            // 截取后面多余
            eventExplainTemplate = substringBeforeThirdSemicolon(eventExplainTemplate, placeholderIndex);
        }
        eventDescTemplate = replaceTemplate(eventDescTemplate,
                "appAlarmTime", firstAlarmTime);
        saveAlarmEvent(entity, entity.getStrategyName(), eventDescTemplate, eventExplainTemplate, handlingSuggestionTemplate,
                sadApp.getUniPositionCode(), sadApp.getDomainName(), sadApp.getLinkCode(), deviceCode, eventIds, appCodes);
        return null;
    }


    /**
     * @param appAlarmList 业务告警集合
     * @param appList      业务注册数据集合
     * @return 根据业务告警时间正序输出业务集合
     */
    private List<SadApp> getAppList(List<EventWarning> appAlarmList, List<SadApp> appList) {

        Map<String, String> appIdToAlarmTimeMap = appAlarmList.stream()
                .filter(eventWarning -> "2001".equals(String.valueOf(eventWarning.getEventType())))
                .collect(Collectors.toMap(
                        EventWarning::getEventObjectId,
                        EventWarning::getAlarmTime,
                        (existing, replacement) -> existing // 如果有重复的appId，保留现有的值
                ));

        // 设置SadApp对象的time字段
        appList.forEach(app -> {
            String alarmTime = appIdToAlarmTimeMap.get(String.valueOf(app.getId()));
            if (alarmTime != null) {
                app.setAlarmTime(alarmTime);
            }
        });

        // 根据 SadApp 对象的 time 字段进行排序
// 过滤掉getAlarmTime为空的SadApp对象，然后按照getAlarmTime排序
        List<SadApp> sortedAppList = appList.stream()
                .filter(app -> app.getAlarmTime() != null) // 过滤掉getAlarmTime为空的对象
                .sorted(Comparator.comparing(SadApp::getAlarmTime)) // 按照getAlarmTime排序
                .collect(Collectors.toList()); // 收集结果回列表

        return sortedAppList;
    }

    /**
     * @param entity
     * @param strategyName               事件名称
     * @param eventDescTemplate
     * @param eventExplainTemplate
     * @param handlingSuggestionTemplate
     * @param positionCode
     * @param domainName
     * @param linkCode
     * @param deviceCode
     * @param eventIds
     * @param appCodes
     */
    private void saveAlarmEvent(AlarmEventStrategyEntity entity, String strategyName, String eventDescTemplate, String eventExplainTemplate, String handlingSuggestionTemplate, String positionCode, String domainName, String linkCode, String deviceCode, String eventIds, String appCodes) {
        AlarmEventDTO alarmEventDTO = new AlarmEventDTO();
        alarmEventDTO.setEventName(strategyName);
        alarmEventDTO.setEventLevel(entity.getEventLevel());
        alarmEventDTO.setEventWarnIds(eventIds);
        alarmEventDTO.setEventDesc(eventDescTemplate);
        alarmEventDTO.setEventExplain(eventExplainTemplate);
        alarmEventDTO.setHandlingSuggestion(handlingSuggestionTemplate);
        alarmEventDTO.setStatus(0);
        alarmEventDTO.setDomainName(domainName);
        alarmEventDTO.setUniPositionCode(positionCode);
        alarmEventDTO.setLinkCodes(linkCode);
        alarmEventDTO.setAppCodes(appCodes);
        alarmEventDTO.setDeviceCodes(deviceCode);
        alarmEventDTO.setStrategyId(entity.getId());
        alarmEventDao.saveAlarmEvent(alarmEventDTO);
    }

    private String getEventDescTemplate(String deviceRemark, Integer deviceEventType, JSONObject handlingSuggestionJson) {
        String eventDescTemplate = "";
        switch (deviceEventType) {
            case 3001:
                eventDescTemplate = handlingSuggestionJson.getString("hardware");
                break;
            case 3005:
                eventDescTemplate = handlingSuggestionJson.getString("process");
                break;
            case 3002:
                eventDescTemplate = getDeviceSpecificTemplate(deviceRemark, handlingSuggestionJson);
                if (StringUtils.isBlank(eventDescTemplate)) {  // 事件还原
                    eventDescTemplate = handlingSuggestionJson.getString("base");
                }
                break;
            case 1004:
                eventDescTemplate = handlingSuggestionJson.getString("illefal");
                break;
            case 1005:
                eventDescTemplate = handlingSuggestionJson.getString("unregistered");
                break;
            case 4001:
                eventDescTemplate = handlingSuggestionJson.getString("sensitive");
                break;
            default:
                break;
        }
        return eventDescTemplate;
    }

    private String getDeviceSpecificTemplate(String deviceRemark, JSONObject handlingSuggestionJson) {
        Map<String, String> deviceKeywordToTemplate = new HashMap<>();
        deviceKeywordToTemplate.put("内存", "memory");
        deviceKeywordToTemplate.put("CPU", "cpu");
        deviceKeywordToTemplate.put("磁盘", "disk");

        for (Map.Entry<String, String> entry : deviceKeywordToTemplate.entrySet()) {
            if (deviceRemark.contains(entry.getKey()) || deviceRemark.contains(entry.getValue())) {
                return handlingSuggestionJson.getString(entry.getValue());
            }
        }

        return "";
    }

    /**
     * 根据告警描述匹配 设备ip
     *
     * @param remark
     * @return
     */
    private String getWarnDeviceIp(String remark) {
        // 正则表达式匹配括号内的 IP 地址
        Pattern pattern = Pattern.compile("((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}");
        Matcher matcher = pattern.matcher(remark);

        if (matcher.find()) {
            // 输出匹配到的 IP 地址
            return matcher.group();
        }
        return null;
    }

    /**
     * 根据告警类型获取模型大类名称
     *
     * @param eventType
     * @return
     */
    private String getDeviceModelName(Integer eventType) {
        String deviceModelName = alarmItemDao.queryAlarmName(eventType);
        return deviceModelName;
    }


    private String generatePlaceholder(String baseName, int index) {
        return baseName + index;
    }

    private String replaceTemplate(String template, String placeholder, String value) {
        if (StringUtils.isNotBlank(value)) {
            value = value.replace("'", "''"); // 防止SQL注入等问题
        } else {
            value = "--";
        }
        return template.replace(placeholder, value);
    }

    public String getStartTime(LocalDateTime now, int timeWindow, DateTimeFormatter formatter) {
        // 计算往前推timeWindow秒的时间作为开始时间
        LocalDateTime startTime = now.minusSeconds(timeWindow);
        // 将开始时间和结束时间转换为字符串
        String startTimeStr = startTime.format(formatter);
        // 输出开始时间和结束时间
        return startTimeStr;
    }


}
