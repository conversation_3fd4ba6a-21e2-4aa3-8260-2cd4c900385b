package com.sailing.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sailing.cas.CasFilter;
import com.sailing.cas.entity.CasUser;
import com.sailing.common.CmUnitUtil;
import com.sailing.common.ManagerPro;
import com.sailing.dao.IWorkOrderDao;
import com.sailing.dao.TEventWarnDao;
import com.sailing.dto.*;
import com.sailing.entity.EmailConfig;
import com.sailing.entity.WorkOrderEntity;
import com.sailing.scms.ICmLinkDao;
import com.sailing.scms.ISaDApplyDao;
import com.sailing.sdp.orm.elasticsearch.util.CollectionUtils;
import com.sailing.service.IEmailConfigService;
import com.sailing.service.WorkOrderService;
import com.sailing.util.Client;
import com.sailing.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName WorkOrderServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/3/12 14:02
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {

    @Autowired
    private IWorkOrderDao workOrderDao;

    @Autowired
    private ICmLinkDao cmLinkDao;

    @Autowired
    private ISaDApplyDao saDApplyDao;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TEventWarnDao tEventWarnDao;

    @Autowired
    private IEmailConfigService emailConfigService;

    private final String WORK_ORDER = "workOrder:";

    // 序列号范围（000001-999999）
    private static final int MAX_SEQ = 1000000;

    private static ExecutorService mExecutor = Executors.newCachedThreadPool();

    // 日期格式
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public PageUtil queryPage(QueryWorkOrderDTO dto) throws Exception {
        //查询当前机构及子机构
        dto.setUniPositionCodeList(CmUnitUtil.getUniPositionCodeChildren(null));
        Page<Object> page = PageHelper.startPage(dto.getCurrent(), dto.getPageSize());
        List<WorkOrderEntity> workOrderEntities = workOrderDao.queryList(dto);
        List<WorkOrderDTO> dtos = new ArrayList<>();
        for (WorkOrderEntity entity : workOrderEntities) {
            WorkOrderDTO dto1 = new WorkOrderDTO();
            BeanUtil.copyProperties(entity, dto1);
            if (StrUtil.isNotEmpty(dto1.getAlerting())) {
                dto1.setEventNum(JSONUtil.toList(dto1.getAlerting(), AlarmDutyPersonInfoDTO.class).size());
            }
            dtos.add(dto1);
        }
        return new PageUtil(dtos, page.getTotal(), dto.getPageSize(), dto.getCurrent());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(WorkOrderDTO dto) throws Exception {
        List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfo = getAlarmDutyPersonInfo(dto.getAlarmIdList());
        dto.setAlerting(JSONUtil.toJsonStr(alarmDutyPersonInfo));
        //修改告警信息
        if (dto.getId() != null) {
            return workOrderDao.update(dto);
        }
        //新增时自动添加编码
        dto = getOrderCode(dto);
        if (StrUtil.isBlank(dto.getOrderName())) {
            dto.setOrderName("工单-" + dto.getOrderCode());
        }
        //创建人员
        dto.setCreator(CasFilter.getCasUser().getUserName());
        //前端没有数据的话获取当前站点
        dto.setUniPositionCode(StrUtil.isNotBlank(dto.getUniPositionCode()) ? dto.getUniPositionCode() : CmUnitUtil.getUserPreUniPositionCode());
        //发送短信
        if (workOrderDao.save(dto)) {
            //告警关联工单
            tEventWarnDao.insertWorkOrder(dto.getOrderCode(), dto.getAlarmIdList());
            SendSmsTask sendSmsTask = new SendSmsTask(dto.getOrderCode(), dto.getDutyMobile(), dto.getOrganizationMobile(), alarmDutyPersonInfo, emailConfigService);
            mExecutor.submit(sendSmsTask);
            return true;
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatue(WorkOrderStatusDTO dto) {
        if (null == dto || null == dto.getId()) {
            log.debug("工单ID不能为空");
            return false;
        }
        //撤销工单时删除告警侧关联的工单
        if (dto.getStatus() == 3) {
            WorkOrderEntity detailById = workOrderDao.getDetailById(dto.getId());
            String alerting = detailById.getAlerting();
            if (StrUtil.isNotBlank(alerting)) {
                List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOList = JSONUtil.toList(alerting, AlarmDutyPersonInfoDTO.class);
                alarmDutyPersonInfoDTOList.stream().forEach(item -> {
                    tEventWarnDao.deleteWorkOrder(item.getAlarmId().toString());
                });
            }
        }
        if (0 != dto.getStatus()) {
            return workOrderDao.updateStatue(dto);
        }
        return false;
    }


    @Override
    public WorkOrderDTO getDetail(Map<String, String> map) {

        WorkOrderEntity entity = new WorkOrderEntity();
        if (map.containsKey("id")) {//通过ID查询
            entity = workOrderDao.getDetailById(Long.valueOf(map.get("id")));
        } else if (map.containsKey("orderCode")) {//通过工单编码查询
            entity = workOrderDao.getDetailByOrderCode(map.get("orderCode"));
        }

        if (ObjectUtil.isEmpty(entity)) {
            return new WorkOrderDTO();
        }
        WorkOrderDTO dto = BeanUtil.copyProperties(entity, WorkOrderDTO.class);
        if (StrUtil.isNotEmpty(dto.getAlerting())) {
            List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOList = JSONUtil.toList(dto.getAlerting(), AlarmDutyPersonInfoDTO.class);
            dto.setAlarmDutyPersonInfoDTOList(alarmDutyPersonInfoDTOList);
            // 更新状态信息
            updateStatuses(dto.getAlarmDutyPersonInfoDTOList());
        }

        return dto;
    }


    @Override
    public List<DutyPersonInfoDTO> getDutyPersonInfo(List<String> alarmIds) {
        if (CollectionUtils.isEmpty(alarmIds)) {
            return Collections.emptyList();
        }
        List<DutyPersonInfoDTO> dutyPersonInfo = new ArrayList<>();
        Map<String, String> result = workOrderDao.getDutyPersonInfo(alarmIds);
        if (null == result) {
            return Collections.emptyList();
        }
        String eventType = "";
        //单个业务告警查询自动回显责任人
        if (alarmIds.size() == 1) {
            eventType = workOrderDao.getEventType(alarmIds.get(0));
        }
        //链路管理人员名称、电话
        if (result.containsKey("linkId")) {
            List<String> linkIds = Arrays.asList(result.get("linkId").split(","));
            List<DutyPersonInfoDTO> dutyPersonInfo1 = cmLinkDao.getDutyPersonInfo(linkIds);
            dutyPersonInfo.addAll(setDisPlay(dutyPersonInfo1, "linkId", eventType));
        }
        //业务管理人员名称、电话
        if (result.containsKey("eventObjectId")) {
            List<String> applyIds = Arrays.asList(result.get("eventObjectId").split(","));
            List<DutyPersonInfoDTO> dutyPersonInfo1 = saDApplyDao.getDutyPersonInfo(applyIds);
            dutyPersonInfo.addAll(setDisPlay(dutyPersonInfo1, "eventObjectId", eventType));
        }
        //如果只有一个告警且没有默认回显，则第一个责任人默认回显
        if (!CollectionUtils.isEmpty(dutyPersonInfo) && StrUtil.isNotEmpty(eventType) && dutyPersonInfo.size() == 1) {
            if (ObjectUtil.isNotEmpty(dutyPersonInfo.get(0))) {
                dutyPersonInfo.get(0).setDisplay(1);
            }
        }

        return distinct(dutyPersonInfo);
    }

    /**
     * 单个业务时有回显，eventType为空说明不止一个责任人，此时不回显，eventType有值时说明此工单只存在一个告警
     *
     * @param dutyPersonInfo
     * @param key
     * @param eventType
     * @return
     */
    private List<DutyPersonInfoDTO> setDisPlay(List<DutyPersonInfoDTO> dutyPersonInfo, String key, String eventType) {
        //只有单个告警时回显
        if (StrUtil.isEmpty(eventType) || CollectionUtils.isEmpty(dutyPersonInfo)) {
            return dutyPersonInfo;
        }
        Iterator<DutyPersonInfoDTO> iterator = dutyPersonInfo.iterator();
        while (iterator.hasNext()) {
            DutyPersonInfoDTO next = iterator.next();
            if (ObjectUtil.isNull(next) || StrUtil.isEmpty(next.getDutyName()) || StrUtil.isEmpty(next.getDutyMobile())) {
                iterator.remove();
                continue;
            }
            //业务类型告警
            if (key.equals("eventObjectId") && eventType.startsWith("2")) {
                next.setDisplay(1);
                return dutyPersonInfo;
            }
            //其他类型告警
            if (key.equals("linkId") && !eventType.startsWith("2")) {
                next.setDisplay(1);
                return dutyPersonInfo;
            }
        }
        return dutyPersonInfo;
    }


    @Override
    public List<AlarmDutyPersonInfoDTO> getAlarmDutyPersonInfo(List<String> alarmIds) {
        if (CollectionUtils.isEmpty(alarmIds)) {
            return Collections.emptyList();
        }

        List<AlarmDescDTO> alarmDescDTOS = tEventWarnDao.queryAlarmById(alarmIds);
        List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOs = new ArrayList<>();

        for (AlarmDescDTO alarmDescDTO : alarmDescDTOS) {
            AlarmDutyPersonInfoDTO dutyPersonInfoDTO = new AlarmDutyPersonInfoDTO();
            dutyPersonInfoDTO.setAlarmId(alarmDescDTO.getAlarmId());
            dutyPersonInfoDTO.setRemark(alarmDescDTO.getRemark());
            dutyPersonInfoDTO.setLevel(alarmDescDTO.getLevel());
            // 获取职责人员信息
            List<DutyPersonInfoDTO> dutyPersonInfo = getDutyPersonInfo(alarmDescDTO);

            if (!CollectionUtils.isEmpty(dutyPersonInfo)) {
                DutyPersonInfoDTO person = dutyPersonInfo.get(0) == null ? new DutyPersonInfoDTO() : dutyPersonInfo.get(0);
                dutyPersonInfoDTO.setDutyName(person.getDutyName());
                dutyPersonInfoDTO.setDutyMobile(person.getDutyMobile());
            }

            alarmDutyPersonInfoDTOs.add(dutyPersonInfoDTO);
        }

        return alarmDutyPersonInfoDTOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEventOrderCode(String alarmId) {
        if (tEventWarnDao.deleteWorkOrder(alarmId) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取责任人员信息
     *
     * @param alarmDescDTO
     * @return
     */
    private List<DutyPersonInfoDTO> getDutyPersonInfo(AlarmDescDTO alarmDescDTO) {
        String id = "";
        //业务类告警以2开头
        if (alarmDescDTO.getEventType().startsWith("2")) {
            id = alarmDescDTO.getEventObjectId();
            if (StrUtil.isEmpty(id)) {
                id = alarmDescDTO.getLinkId();
            }
        } else {
            id = alarmDescDTO.getLinkId();
        }

        if (StrUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        if (alarmDescDTO.getEventType().startsWith("2") && StrUtil.isNotEmpty(alarmDescDTO.getEventObjectId())) {
            return saDApplyDao.getDutyPersonInfo(Collections.singletonList(id));
        }

        return cmLinkDao.getDutyPersonInfo(Collections.singletonList(id));

    }

    /**
     * 生成工单编码
     *
     * @param dto 工单DTO对象
     * @return 填充了编码的工单DTO对象
     */
    private WorkOrderDTO getOrderCode(WorkOrderDTO dto) {
        if (dto == null) {
            return null;
        }
        String orderNumber = generateOrderNumber();
        dto.setOrderCode(orderNumber);
        return dto;
    }

    /**
     * 生成工单号
     *
     * @return
     */
    public String generateOrderNumber() {

        String timestamp = LocalDateTime.now().format(DATE_FORMAT);
        String redisKey = WORK_ORDER + timestamp;

        Long sequence = redisTemplate.opsForValue().increment(redisKey);
        if (sequence == null) {
            throw new RuntimeException("Failed to generate sequence number for work order.");
        }
        if (sequence == 1) {
            //设置24小时过期
            redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);
        }

        // 格式化序列为 6 位数字，不足 6 位前面补 0
        String sequenceStr = String.format("%06d", sequence % MAX_SEQ);

        // 格式：BSC + 时间戳 + 序列号
        return String.format("BSC%s%s", timestamp, sequenceStr);
    }

    /**
     * 责任人信息去重，电话为空的去除
     *
     * @param list
     * @return
     */
    private List<DutyPersonInfoDTO> distinct(List<DutyPersonInfoDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream()
                .filter(dto -> dto != null && !StrUtil.isEmpty(dto.getDutyMobile()))
                .collect(Collectors.collectingAndThen(Collectors.toMap(
                                DutyPersonInfoDTO::getDutyMobile, dto -> dto, (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())));
    }


    /**
     * 返回的告警详情中修改处置状态
     *
     * @param alarmDutyPersonInfoDTOList
     */
    private void updateStatuses(List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOList) {
        if (CollectionUtils.isEmpty(alarmDutyPersonInfoDTOList)) {
            return;
        }

        List<Long> alarmIds = alarmDutyPersonInfoDTOList.stream()
                .map(AlarmDutyPersonInfoDTO::getAlarmId)
                .filter(Objects::nonNull) // 确保 AlarmId 不为空
                .collect(Collectors.toList());

        Map<Long, String> statusMap = queryStatusMap(alarmIds);

        for (AlarmDutyPersonInfoDTO alarmDutyPersonInfoDTO : alarmDutyPersonInfoDTOList) {
            Long alarmId = alarmDutyPersonInfoDTO.getAlarmId();
            if (alarmId != null && statusMap.containsKey(alarmId)) {
                alarmDutyPersonInfoDTO.setStatus(Integer.parseInt(statusMap.get(alarmId)));
            }
        }
    }

    /**
     * 获取告警处置状态
     *
     * @param ids
     * @return
     */
    public Map<Long, String> queryStatusMap(List<Long> ids) {
        List<Map<String, Object>> rawResults = tEventWarnDao.queryStatusMapRaw(ids);
        Map<Long, String> resultMap = new HashMap<>();
        for (Map<String, Object> row : rawResults) {
            Long id = (Long) row.get("id");
            String status = (String) row.get("status");
            resultMap.put(id, status);
        }
        return resultMap;
    }

    /**
     * 发送短信内部线程
     */
    class SendSmsTask implements Runnable {
        // 工单编号
        private final String orderCode;
        // 责任人手机号
        private final String dutyMobile;
        // 机构负责人手机号
        private final String organizationMobile;
        // 告警责任人信息列表
        private final List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOList;

        private final IEmailConfigService emailConfigService;

        public SendSmsTask(String orderCode, String dutyMobile, String organizationMobile,
                           List<AlarmDutyPersonInfoDTO> alarmDutyPersonInfoDTOList, IEmailConfigService emailConfigService) {
            this.orderCode = orderCode;
            this.dutyMobile = dutyMobile;
            this.organizationMobile = organizationMobile;
            this.alarmDutyPersonInfoDTOList = alarmDutyPersonInfoDTOList;
            this.emailConfigService = emailConfigService;
        }

        @Override
        public void run() {
            if (CollectionUtils.isEmpty(alarmDutyPersonInfoDTOList)) {
                return;
            }

            EmailConfig emailConfig = fetchEmailConfig();
            if (null == emailConfig) {
                log.info("未配置短信信息！！！工单【{}】不发送短信！！！", orderCode);
            }
            String url = emailConfig.getSmsUrl();
            String customParam = emailConfig.getCustomParam();
            String smsKey = ManagerPro.getString("smsKey");
            Integer requestMethod = emailConfig.getRequestMethod();

            for (AlarmDutyPersonInfoDTO dto : alarmDutyPersonInfoDTOList) {
                if (dto.getLevel() != null) {
                    String levelMsg = getLevelMessage(dto.getLevel());
                    String content = String.format("工单编号 【%s】 本短信是告警信息,%s【%s】", orderCode, dto.getRemark(), smsKey);
                    sendSms(url, customParam, content, dutyMobile, requestMethod, levelMsg);
                    if (!StrUtil.isEmpty(organizationMobile)) {
                        sendSms(url, customParam, content, organizationMobile, requestMethod, levelMsg);
                    }
                }
            }
        }

        /**
         * 获取短信配置
         *
         * @return
         */
        private EmailConfig fetchEmailConfig() {
            EmailConfig emailConfig = new EmailConfig();
            try {
                emailConfig = emailConfigService.get(emailConfig);
            } catch (Exception e) {
                throw new RuntimeException("Failed to fetch email configuration", e);
            }
            return emailConfig;
        }

        /**
         * 获取告警等级
         *
         * @param level
         * @return
         */
        private String getLevelMessage(int level) {
            switch (level) {
                case 1:
                    return "一般";
                case 2:
                    return "严重";
                case 3:
                    return "紧急";
                default:
                    return "";
            }
        }

        private void sendSms(String url, String customParam, String content, String mobile, Integer requestMethod, String levelMsg) {
            Client.sendSms2(url, customParam, content, mobile, requestMethod, levelMsg);
        }
    }
}
