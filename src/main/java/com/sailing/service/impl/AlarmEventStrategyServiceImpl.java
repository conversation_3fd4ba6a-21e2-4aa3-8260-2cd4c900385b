package com.sailing.service.impl;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sailing.dao.IAlarmEventStrategyDao;
import com.sailing.dto.AlarmEventStrategyDTO;
import com.sailing.dto.SearchAlarmEventStrategyDTO;
import com.sailing.entity.AlarmEventStrategyEntity;
import com.sailing.service.IAlarmEventStrategyService;
import com.sailing.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName AlarmEventStrategyServiceImpl
 * @Description 安全策略实现类
 * <AUTHOR>
 * @Date 2024/12/9 14:17
 */
@Service
@Slf4j
public class AlarmEventStrategyServiceImpl implements IAlarmEventStrategyService {

    @Autowired
    private IAlarmEventStrategyDao alarmEventStrategyDao;

    @Override
    public PageUtil queryPage(SearchAlarmEventStrategyDTO dto) {
        try (Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())) {
            List<AlarmEventStrategyEntity> list = alarmEventStrategyDao.queryAll(dto);
            return new PageUtil(list, (int) page.getTotal(), dto.getPageSize(), dto.getPageNum());
        }
    }

    @Override
    public boolean saveAlarmEventStrategy(AlarmEventStrategyDTO dto) {
        AlarmEventStrategyEntity alarmEventStrategyEntity = alarmEventStrategyDao.queryByName(dto.getStrategyName());
        if (null != alarmEventStrategyEntity) {
            throw new ApiException("安全策略名称已存在，请重新输入！！！");
        }
        if (!isValidJson(dto.getConfig())) {
            throw new ApiException("策略规则配置不为json格式，请检查参数！！！");
        }
        int save = alarmEventStrategyDao.save(dto);
        if (save > 0) {
            return true;
        }
        return false;
    }

    @Override
    public AlarmEventStrategyEntity queryById(Long id) {
        return alarmEventStrategyDao.queryById(id);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        if (alarmEventStrategyDao.deleteByIds(ids) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteById(Long id) {
        AlarmEventStrategyEntity alarmEventStrategyEntity = alarmEventStrategyDao.queryById(id);
        if (null == alarmEventStrategyEntity) {
            throw new ApiException("当前安全策略不存在或已被删除！！！");
        }
        List<Long> ids = new ArrayList<>();
        ids.add(id);
        return deleteByIds(ids);
    }

    @Override
    public boolean updateById(AlarmEventStrategyDTO dto) {
        if (null == dto || null == dto.getId()) {
            throw new ApiException("参数异常！安全策略ID不能为空！！！");
        }
        if (!isValidJson(dto.getConfig())) {
            throw new ApiException("策略规则配置不为json格式，请检查参数");
        }
        AlarmEventStrategyEntity alarmEventStrategyEntity = alarmEventStrategyDao.queryById(dto.getId());
        if (null == alarmEventStrategyEntity) {
            throw new ApiException("当前安全策略不存在或已被删除！！！");
        }
        AlarmEventStrategyEntity entity = alarmEventStrategyDao.queryByName(dto.getStrategyName());
        if (entity != null) {
            throw new ApiException("当前安全策略名称已存在！！！");
        }
        if (alarmEventStrategyDao.update(dto) > 0) {
            return true;
        }
        return false;
    }

    public static boolean isValidJson(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            JSONUtil.parseObj(str);
            return true;
        } catch (Exception e) {
            log.error("安全事件策略规则配置不是json格式：{}", str);
            return false;
        }
    }
}
