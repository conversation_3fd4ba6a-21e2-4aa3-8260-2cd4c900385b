package com.sailing.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sailing.common.DateUtil;
import com.sailing.dao.TEventWarnDao;
import com.sailing.enume.EventClassifyEnum;
import com.sailing.nta.ICmAppDao;
import com.sailing.scms.IDeviceDao;
import com.sailing.scms.ISaDApplyDao;
import com.sailing.service.TEventWarnService;
import com.sailing.util.PageUtil;
import com.sailing.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 安全事件记录查询服务实现
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Service("tEventWarnService")
public class TEventWarnServiceImpl implements TEventWarnService {
    @Resource
    private TEventWarnDao tEventWarnDao;
    @Resource
    private IDeviceDao deviceDao;
    @Resource
    private ISaDApplyDao applyDao;
    @Resource
    private ICmAppDao cmAppDao;

    @Override
    public PageUtil queryPage(TEventWarnReq req) {
        try (Page<?> page = PageHelper.startPage(req.getPage(), req.getSize())) {
            if (req.getEventClassify() != null) {
                req.setEventClassify(EventClassifyEnum.getCode(req.getEventClassify()));
            }
            List<TEventWarnResp> list = tEventWarnDao.queryList(req);
            return new PageUtil(list, (int) page.getTotal(), page.getPageSize(), page.getPageNum());
        }
    }

    /**
     * 资产列表查询
     * @param req 资产列表查询参数
     * @return 查询结构
     */
    @Override
    public PageUtil deviceList(AssetListReq req) {
        try (Page<?> page = com.github.pagehelper.PageHelper.startPage(req.getPage(), req.getSize())) {
            List<AssetListResp> list = deviceDao.deviceList();
            return new PageUtil(list, (int) page.getTotal(), page.getPageSize(), page.getPageNum());
        }
    }

    /**
     * 业务和当日流量统计信息查询
     *
     * @param req 业务和当日流量统计信息查询参数
     * @return 查询结构
     */
    @Override
    public List<BizStatisticsResp> bizStatistics(BizStatisticsReq req) {
        List<BizStatisticsResp> list = applyDao.queryAppAndLinkCountByDomain(req.getDomain());
        if (req.getQueryTime() == null) {
            req.setQueryTime(DateUtil.getCurrentDateFormat(DateUtil.DateFormat_yyyy_MM_dd_HH_mm_ss));
        }
        applyDao.queryLinkWithDomain(req.getDomain());
        List<LinkDayFlow> linkDayFlowList = cmAppDao.queryLinkDayFlow(req.getQueryTime());
        return applyDao.queryAppAndLinkCountByDomain(req.getDomain());
    }
}