package com.sailing.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sailing.common.DateUtil;
import com.sailing.dao.TEventWarnDao;
import com.sailing.enume.EventClassifyEnum;
import com.sailing.nta.ICmAppDao;
import com.sailing.scms.IDeviceDao;
import com.sailing.scms.ISaDApplyDao;
import com.sailing.service.TEventWarnService;
import com.sailing.util.PageUtil;
import com.sailing.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("tEventWarnService")
public class TEventWarnServiceImpl implements TEventWarnService {
    @Resource
    private TEventWarnDao tEventWarnDao;
    @Resource
    private IDeviceDao deviceDao;
    @Resource
    private ISaDApplyDao applyDao;
    @Resource
    private ICmAppDao cmAppDao;


    @Override
    public PageUtil queryPage(TEventWarnReq req) {
        try (Page<?> page = PageHelper.startPage(req.getPage(), req.getSize())) {
            if (req.getEventClassify() != null) {
                req.setEventClassify(EventClassifyEnum.getCode(req.getEventClassify()));
            }
            List<TEventWarnResp> list = tEventWarnDao.queryList(req);
            return new PageUtil(list, (int) page.getTotal(), page.getPageSize(), page.getPageNum());
        }
    }

    /**
     * 资产列表查询
     * @param req 资产列表查询参数
     * @return 查询结构
     */
    @Override
    public PageUtil deviceList(AssetListReq req) {
        try (Page<?> page = com.github.pagehelper.PageHelper.startPage(req.getPage(), req.getSize())) {
            List<AssetListResp> list = deviceDao.deviceList();
            return new PageUtil(list, (int) page.getTotal(), page.getPageSize(), page.getPageNum());
        }
    }
    /**
     * 业务和当日流量统计信息查询
     *
     * @param req 业务当日流量统计信息查询参数
     * @return 查询结果
     */
    @Override
    public List<BizStatisticsResp> bizStatistics(BizStatisticsReq req) {
        // 设置默认查询时间
        if (req.getQueryTime() == null) {
            req.setQueryTime(DateUtil.getCurrentDateFormat(DateUtil.DateFormat_yyyy_MM_dd_HH_mm_ss));
        }

        // 查询业务和链路统计信息
        List<BizStatisticsResp> bizStatisticsList = applyDao.queryAppAndLinkCountByDomain(req.getDomain());
        if (CollUtil.isEmpty(bizStatisticsList)) {
            return Collections.emptyList();
        }

        // 并行查询链路与域关联信息和流量数据
        List<LinkWithDomain> linkWithDomainList = applyDao.queryLinkWithDomain(req.getDomain());
        List<LinkDayFlow> linkDayFlowList = cmAppDao.queryLinkDayFlow(req.getQueryTime());

        // 将流量数据合并到业务统计结果中
        mergeLinkFlowData(bizStatisticsList, linkWithDomainList, linkDayFlowList, req.getQueryTime());

        return bizStatisticsList;
    }

    /**
     * 将链路流量数据合并到业务统计结果中
     * 优化算法：使用Map提高查找效率，避免嵌套循环
     *
     * @param bizStatisticsList 业务统计列表
     * @param linkWithDomainList 链路与域关联列表
     * @param linkDayFlowList 链路流量列表
     * @param queryTime 查询时间
     */
    private void mergeLinkFlowData(List<BizStatisticsResp> bizStatisticsList,
        List<LinkWithDomain> linkWithDomainList,
        List<LinkDayFlow> linkDayFlowList,
        String queryTime) {
        if (CollUtil.isEmpty(bizStatisticsList)) {
            return;
        }

        // 创建链路流量映射表，提高查找效率
        Map<Integer, Long> linkFlowMap = Optional.ofNullable(linkDayFlowList)
            .orElse(Collections.emptyList())
            .stream()
            .filter(flow -> flow.getLinkId() != null && flow.getDataFlow() != null)
            .collect(Collectors.toMap(
                LinkDayFlow::getLinkId,
                LinkDayFlow::getDataFlow,
                Long::sum // 如果有重复的linkId，就累加流量
            ));

        // 创建域到链路ID集合的映射
        Map<String, Set<Integer>> domainToLinkIdsMap = Optional.ofNullable(linkWithDomainList)
            .orElse(Collections.emptyList())
            .stream()
            .filter(link -> link.getDomain() != null && link.getLinkId() != null)
            .collect(Collectors.groupingBy(
                LinkWithDomain::getDomain,
                Collectors.mapping(LinkWithDomain::getLinkId, Collectors.toSet())
            ));

        // 为每个业务统计结果设置流量数据和查询时间
        for (BizStatisticsResp bizStats : bizStatisticsList) {
            // 设置查询时间
            bizStats.setTime(queryTime);

            // 计算该域下所有链路的总流量
            String domain = bizStats.getDomain();
            if (domain != null) {
                Set<Integer> linkIds = domainToLinkIdsMap.get(domain);
                if (linkIds != null && !linkIds.isEmpty()) {
                    long totalFlow = linkIds.stream()
                        .mapToLong(linkId -> linkFlowMap.getOrDefault(linkId, 0L))
                        .sum();
                    bizStats.setDataFlow(totalFlow);
                } else {
                    bizStats.setDataFlow(0L);
                }
            } else {
                bizStats.setDataFlow(0L);
            }
        }
    }
}