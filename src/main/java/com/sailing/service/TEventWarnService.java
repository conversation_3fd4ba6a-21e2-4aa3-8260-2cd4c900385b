package com.sailing.service;

import com.sailing.util.PageUtil;
import com.sailing.vo.AssetListReq;
import com.sailing.vo.BizStatisticsReq;
import com.sailing.vo.BizStatisticsResp;
import com.sailing.vo.TEventWarnReq;

import java.util.List;

/**
 * @Description 安全事件记录查询服务层
 * <AUTHOR>
 * @date 2024/5/12 15:05
 * @Version 1.0
 */
public interface TEventWarnService {

    PageUtil queryPage(TEventWarnReq req);

    /**
     * 资产列表查询
     * @param req 资产列表查询参数
     * @return 查询结构
     */
    PageUtil deviceList(AssetListReq req);

    /**
     * 业务和当日流量统计信息查询
     * @param req 业务和当日流量统计信息查询参数
     * @return 查询结构
     */
    List<BizStatisticsResp> bizStatistics(BizStatisticsReq req);
}

