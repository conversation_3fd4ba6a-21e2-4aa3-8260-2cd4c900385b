package com.sailing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 业务和当日流量统计信息查询结果
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Data
@ApiModel("业务和当日流量统计信息查询结果")
public class BizStatisticsResp {
    /**
     * 查询时间
     */
    @ApiModelProperty("所属网域")
    private String domain;
    /**
     * 链路总数
     */
    @ApiModelProperty("链路总数")
    private Integer linkCount;
    /**
     * 业务总数
     */
    @ApiModelProperty("业务总数")
    private Integer bizCount;
    /**
     * 今日总流量（单位Byte）
     */
    @ApiModelProperty("今日总流量（单位Byte）")
    private Long dataFlow;
    /**
     * 查询流量时间(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty("查询流量时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String time;
}
