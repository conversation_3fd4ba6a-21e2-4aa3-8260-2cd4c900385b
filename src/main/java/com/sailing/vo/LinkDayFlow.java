package com.sailing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务和当日流量统计信息查询结果
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Data
@ApiModel("链路当日流量统计信息")
public class LinkDayFlow {
    /**
     * 链路总数
     */
    @ApiModelProperty("链路总数")
    private Integer linkId;
    /**
     * 今日总流量（单位Byte）
     */
    @ApiModelProperty("今日总流量（单位Byte）")
    private Long dataFlow;
}
