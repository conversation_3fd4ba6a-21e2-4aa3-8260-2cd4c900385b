package com.sailing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 资产列表查询参数
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Data
@ApiModel("资产列表查询参数")
public class AssetListReq implements Serializable {
    private static final long serialVersionUID = -2894388574374534094L;
    /**
     * 分页数
     */
    @NotNull
    @Min(value = 1, message = "page must be at least 1")
    @ApiModelProperty("分页数")
    private Integer page;
    /**
     * 每页数
     */
    @NotNull
    @Min(value = 1, message = "size must be at least 1")
    @ApiModelProperty("每页数")
    private Integer size;

    /**
     * 查询开始时间
     */
    @ApiModelProperty("查询开始时间（yyyy-MM-dd HH:mm:ss）")
    @Pattern(
        regexp = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9]:[0-5][0-9])$+",
        message = "startTime not format yyyy-MM-dd HH:mm:ss")
    private String startTime;
    /**
     * 查询结束时间
     */
    @Pattern(
        regexp = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9]:[0-5][0-9])$+",
        message = "startTime not format yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("查询结束时间（yyyy-MM-dd HH:mm:ss）")
    private String endTime;

}
