package com.sailing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 资产列表查询参数
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Data
@ApiModel("资产列表查询参数")
public class AssetListReq implements Serializable {
    private static final long serialVersionUID = -2894388574374534094L;
    /**
     * 分页数
     */
    @NotNull
    @Min(value = 1, message = "page must be at least 1")
    @ApiModelProperty("分页数")
    private Integer page;
    /**
     * 每页数
     */
    @NotNull
    @Min(value = 1, message = "size must be at least 1")
    @ApiModelProperty("每页数")
    private Integer size;
}
