package com.sailing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 链路与域关联信息
 *
 * <AUTHOR>
 * @since 2025/03/17 15:05
 */
@Data
@ApiModel("链路与域关联信息")
public class LinkWithDomain {
    /**
     * 链路ID
     */
    @ApiModelProperty("链路ID")
    private Integer linkId;

    /**
     * 链路名称
     */
    @ApiModelProperty("链路名称")
    private String linkName;

    /**
     * 域名
     */
    @ApiModelProperty("域名")
    private String domain;

    /**
     * 域ID
     */
    @ApiModelProperty("域ID")
    private Long domainId;
}
