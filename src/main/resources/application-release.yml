spring:
  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    jdbc-url: ***************************************************************************************************************************************
    username: siem
    password: sailing2018
  nta-datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    jdbc-url: jdbc:mysql://************:53306/nta?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true
    username: nta
    password: sailing2018
  scms-datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    jdbc-url: jdbc:mysql://************:53306/cm_db?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true
    username: scms
    password: sailing2018
  job-datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    jdbc-url: jdbc:mysql://************:53306/job_manager?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true
    username: job_manager
    password: sailing2018
  bld-datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    jdbc-url: **************************************************************************************************************************************
    username: bld
    password: sailing2018
  jmx:
    default-domain: siem
  devtools:
    restart:
      enabled: false
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  redis:
    database: 1
    host: ************
    port: 56379
    password: sailing2018
    timeout: 2000
    jedis:
      pool:
        max-idle: 100
        min-idle: 1
        max-active: 300
        max-wait: -1
  extProFile: /opt/microservices/siem/siem.properties
mybatis:
  type-aliases-pack: com.sailing.entity

elasticsearch:
  ip: ************:59200

aop:
  proxy-target-class: true
#true:开发模式，接口不校验登录状态； false:生产模式，接口校验登录状态
developerModel: false

#（0：报警不入库，1：报警入库）
warringDBModel: 1

casClient:
  debug: false
  casServerUrl: https://************:443/public_ws
  filterUrl: /siem/eventWarning/queryListByEventType|/siem/eventWarning/saveOrUpdate|/siem/eventWarning/getByObjId|/siem/eventWarning/saveGeneral|/siem/per/getPermissionByModelCode|/siem/swagger-resources**|/siem/webjars**|/siem/v2**|/siem/swagger-ui.html**|/siem/eventWarning/updateWarning|/siem/eventWarn/queryAll

northbound:
  checkUrl: /siem/eventWarn/queryAll|/siem/biz/asset/list

swaggerShow: true

ntaRemoteHost: ************
bldRemoteHost: ************
scmsRemoteHost: ************
scmsRemoteIp: https://************:9905/scms_ws

topic: netflowTopic
kafkaServer: ************:19092
alarmTopic: alarmTopic
alarmConsumer: alarm-consumer
siemTopic: siemTopic
siemConsumer: siem-consumer

appMonitorCron:
  0 0/10 * * * ?

server:
  servlet:
    context-path: /siem/
    session:
      timeout: 3600
  tomcat:
    basedir: /opt/microservices/siem
    uri-encoding: UTF-8
    accesslog:
      enabled: true
      buffered: true
      directory: ./log
      file-date-format: .yyyy-MM-dd
      pattern: '%h %l %u %t "%r" %s %b "%{Referer}i" "%{User-Agent}i" %D ms'
      prefix: access_log
      rename-on-rotate: false
      request-attributes-enabled: false
      rotate: true
      suffix: .log
  ssl:
    key-store: /opt/microservices/server.key.p12
    key-store-password: sailing2018

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

logging:
  config: /opt/microservices/siem/logback-spring.xml