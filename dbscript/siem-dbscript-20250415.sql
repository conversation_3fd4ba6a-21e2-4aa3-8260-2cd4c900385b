ALTER TABLE `siem`.`t_alarm_event`
    MODIFY  COLUMN  IF EXISTS `domainName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '所属网域';

delete from `t_alarm_event_strategy` where id in (1,2,3,4,5,6);

ALTER TABLE `siem`.`t_alarm_event_strategy`
    MODIFY COLUMN IF EXISTS `eventDescTemplate` JSON NOT NULL COMMENT '事件描述模板',
    MODIFY COLUMN IF EXISTS `handlingSuggestionTemplate` JSON NOT NULL COMMENT '处置建议模板';

INSERT INTO `t_alarm_event_strategy` (`id`, `strategyName`, `strategyDesc`, `timeWindow`, `status`, `priority`, `eventLevel`, `config`, `eventExplainTemplate`, `eventDescTemplate`, `handlingSuggestionTemplate`, `createTime`, `updateTime`, `remarks`) VALUES (1, '设备硬件故障引发流量中断', '', 1200, 1, 1, 3, '{"evnetType":[{"alarmName":"设备状态异常","itemCode":"3001"},{"alarmName":"连续流量中断","itemCode":"2001"},{  "alarmName": "业务流量突增", "itemCode": "2006"  },{ "alarmName": "业务流量突降", "itemCode": "2007" },{ "alarmName": "业务数据堆积", "itemCode": "2009" },{"alarmName":"连续流量过低",  "itemCode": "2017"  },{"alarmName":"链路流量中断","itemCode":"1001"}]}', '{"base":"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备状态异常】告警。\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\n【appAlarmTime3】：还触发了【appTypeName3】告警。\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\n还有appNames等其他业务也受到了影响，流量降为0Mb。\n","hardware":"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\n还有appNames等其他业务也受到了影响，流量降为0Mb。"}', '{"hardware":"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了设备硬件故障引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】"}', '{"base":"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n","hardware":"设备硬件故障：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请核实【deviceType】【deviceIp】地址信息，是否重复分配；\\n④请在【deviceType】【deviceIp】后台使用ethtool等命令行工具查看网卡硬件状态；\\n⑤检查设备电源是否正常供电。\\n"}', '2024-12-09 17:09:40', '2025-04-28 10:03:30', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (2, '资源不足引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备资源不足\",\"itemCode\":\"3002\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\"base\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\r\n\"}', '{\"cpu\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了CPU资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"disk\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了磁盘资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"memory\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了内存资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{\"base\":\"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\"cpu\":\"CPU：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前CPU资源利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查CPU散热系统是否良好；\\n⑤确保断电后，升级CPU的硬件配置。\\n\",\"disk\":\"磁盘：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行df -h命令，核查设备当前磁盘资源利用率是否正处于高位；\\n④请在【deviceType】【deviceIp】清理磁盘空间，删除不必要的文件；\\n⑤增加更多的磁盘空间，并使用磁盘监控工具来识别和解决高I/O的问题。\\n\",\"memory\":\"内存：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前内存利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查内存散热系统是否良好；\\n⑤确保断电后，确认主板是否具备空余内存插槽，增加同频率同容量内存条。\\n\"}', '2024-12-11 10:31:42', '2025-04-10 15:51:30', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (3, '进程故障引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备进程异常\",\"itemCode\":\"3005\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\"process\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\"}', '{\"process\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了设备进程异常引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{  \"base\": \"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\r\n    \"process\": \"进程故障：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③核查【deviceType】【deviceIp】的【processName】，终止或重启非正常或僵尸进程；\\n④请检查任务当前节点编号的进程是否存在，可能是缺少配置导致启动失败，例如单向链路前置部署，后置未部署会导致这种情况发生。\\n\"}', '2024-12-11 10:31:42', '2025-04-10 15:52:32', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (4, '违规透传通道威胁内网安全', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"违规透传通道威胁内网安全\",\"itemCode\":\"1004\"}]}', '{\"illefal\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下违规透传事件：\\n【linkAlarmTime】：涉及的设备类型为【边界前置机】设备(IP deviceIp1，透传端口:devicePort1)，违规访问内网服务器deviceIp2的【devicePort2端口】，建立非法通信通道。\r\n\"}', '{\"illefal\":\"【linkAlarmTime】，因【uniPositionName】【domainName】当中的【边界前置机】，设备ip为【deviceIp1】，存在端口为：devicePort1的违规透传通道，进而导致【linkName】链路发生【违规透传】\"}\r\n', '{  \"base\": \"为确保设备尽快恢复正常运行、减少停机时间并保障业务合规性，请联系对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【边界前置机】（deviceIp1）根据以下措施执行：\\n\",                     \r\n    \"illefal\": \"①立即响应封禁外交换服务器的devicePort1端口通信。\\n②登录服务器核查日志： 登录【边界前置机】（deviceIp1），查看边界交换日志，验证访问源【deviceIp1:devicePort1】是否对内网【deviceIp2:devicePort2】存在更多违规操作。\\n③取证与关闭通道： 完成边界交换日志的取证后，立即关闭违规透传通道，确保内网安全。\\n\"}\r\n	', '2025-03-12 10:22:01', '2025-04-10 15:53:12', '1');
INSERT INTO `t_alarm_event_strategy` VALUES (5, '违规未注册链路', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"违规未注册链路\",\"itemCode\":\"1005\"}]}', '{\"unregistered\":\"【uniPositionName】中，检测识别到以下风险，检测路径如下：\\n【taskStartTime】：用户创建【taskName】对未注册链路启动检测。\\n【taskStopTime】：检测任务识别到IP范围为【borderIp】，IP数量共计有【ipCount】个，其中网络可达IP数量为【accessibleIpCount】个，网络可达率为【achievableRate%】，并检测识别到存在【linkCount】个未注册链路。\\n\",\"base\":\"【alarmTime】：链路IP为【borderIp】，开放端口为【borderPort】所属厂商为【manufacturerName】，设备类型为【deviceType】。\\n\"}', '{\"unregistered\":\"【taskStartTime】，【uniPositionName】的用户启用了未注册链路检测，于【taskStopTime】检测到共计存在linkCount起未注册链路告警。\"}', '{\"base\":\"为确保尽快消除违规数据传输风险并保障链路合规可控，请联系以下厂商关停链路，并提交链路审批流程后启用链路，相关信息如下：\\n\",\"unregistered\":\"【manufacturerName】需核查assetTypeAndIps的边界设备。\\n\"}', '2025-03-12 10:22:01', '2025-04-15 17:20:50', '1');
INSERT INTO `t_alarm_event_strategy` VALUES (6, '敏感数据出网', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"敏感数据出网\",\"itemCode\":\"4001\"}]}', '{\"base\":\"【uniPositionName】【domainName】中，检测识别到【linkName】的【appName】存在以下按时间顺序发生的transMethod数据泄露风险：\\n\",\"sensitive\":\"泄露源IP：【srcIp】，目标IP：【dstIp】\\n【alarmTime1】起，文件名为【fileName1】发生数据泄露，涉及violatContent1等敏感内容。\\n【alarmTime2】起：文件名为【fileName2】发生数据泄露，涉及violatContent2等敏感内容。\\n【alarmTime3】起：文件名为【fileName3】发生数据泄露，涉及violatContent3等敏感内容。\\n【alarmTime4】起：文件名为【fileName4】发生数据泄露，涉及violatContent4等敏感内容。\\n【alarmTime5】起：文件名为【fileName5】发生数据泄露，涉及violatContent5等敏感内容。\r\n\"}', '{\"sensitive\":\"【firstAlarmTime】，识别到【uniPositionName】【domainName】的【linkName】当中的【appName】，涉及alarmCount起敏感数据传输出网。\"}\r\n', '{  \"base\": \"为防止数据泄露范围扩大，请立即联系业务责任人【name】【phone】对上述文件fileNames进行实时监测及回溯，\\n\",                     \r\n    \"sensitive\": \"检查文件访问记录、权限分配及异常操作日志，确认是否存在未授权访问或内部人员违规行为。\\n\"}', '2025-03-12 10:22:01', '2025-04-14 15:15:58', '1');
