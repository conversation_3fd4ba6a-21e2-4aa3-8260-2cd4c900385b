SET FOREIGN_KEY_CHECKS=0;

use siem;

-- ----------------------------
-- Records of t_datadictionaryitem
-- ----------------------------
INSERT INTO `t_datadictionaryitem` VALUES ('1', b'1', '01', '1', '异常端口', '异常端口');
INSERT INTO `t_datadictionaryitem` VALUES ('2', b'1', '01', '2', '异常协议', '异常协议');
INSERT INTO `t_datadictionaryitem` VALUES ('3', b'1', '01', '3', '异常流量', '异常流量');
INSERT INTO `t_datadictionaryitem` VALUES ('5', b'1', '01', '5', '链路未注册', '链路未注册');
INSERT INTO `t_datadictionaryitem` VALUES ('6', b'1', '01', '4', '业务未注册', '业务未注册');
INSERT INTO `t_datadictionaryitem` VALUES ('7', b'1', '01', '6', '违规内容', '违规内容');
INSERT INTO `t_datadictionaryitem` VALUES ('8', b'1', '01', '7', '链路中断', '链路中断');
INSERT INTO `t_datadictionaryitem` VALUES ('9', b'1', '01', '8', '业务中断', '业务中断');
INSERT INTO `t_datadictionaryitem` VALUES ('10', b'1', '01', '9', '链路拥挤', '链路拥挤');
INSERT INTO `t_datadictionaryitem` VALUES ('11', b'1', '01', '10', '业务拥挤', '业务拥挤');
INSERT INTO `t_datadictionaryitem` VALUES ('13', b'1', '01', '99', '其他', '其他');
INSERT INTO `t_datadictionaryitem` VALUES ('14', b'1', '01', '13', '接入设备故障', '接入设备故障');
INSERT INTO `t_datadictionaryitem` VALUES ('15', b'1', '01', '15', '网络攻击', '网络攻击');
INSERT INTO `t_datadictionaryitem` VALUES ('17', b'1', '01', '17', 'N天无流量链路中断', 'N天无流量链路中断');
INSERT INTO `t_datadictionaryitem` VALUES ('18', b'1', '01', '18', '内外网比对链路中断', '内外网比对链路中断');
INSERT INTO `t_datadictionaryitem` VALUES ('19', b'1', '01', '19', '隐匿流量', '隐匿流量');
INSERT INTO `t_datadictionaryitem` VALUES ('20', b'1', '01', '20', '突发流量', '突发流量');
INSERT INTO `t_datadictionaryitem` VALUES ('21', b'1', '01', '21', '突变流量', '突变流量');
INSERT INTO `t_datadictionaryitem` VALUES ('22', b'1', '01', '22', '链路阻塞', '链路阻塞');
INSERT INTO `t_datadictionaryitem` VALUES ('23', b'1', '01', '23', 'N天无流量业务中断', 'N天无流量业务中断');
INSERT INTO `t_datadictionaryitem` VALUES ('24', b'1', '01', '24', '内外网比对业务中断', '内外网比对业务中断');
INSERT INTO `t_datadictionaryitem` VALUES ('25', b'1', '01', '25', '业务阻塞', '业务阻塞');
INSERT INTO `t_datadictionaryitem` VALUES ('26', b'1', '01', '26', '业务内容不符', '业务内容不符');
INSERT INTO `t_datadictionaryitem` VALUES ('27', b'1', '01', '11', '流量异常', '流量异常');
INSERT INTO `t_datadictionaryitem` VALUES ('28', b'1', '01', '14', '应用访问异常', '应用访问异常');
INSERT INTO `t_datadictionaryitem` VALUES (29, b'1', '4001', '1', '链路流量中断模型', '链路流量中断');
INSERT INTO `t_datadictionaryitem` VALUES (30, b'1', '4001', '2', '边界自检异常模型', '边界自检异常');
INSERT INTO `t_datadictionaryitem` VALUES (31, b'1', '4001', '3', '链路自检异常模型', '链路自检异常');
INSERT INTO `t_datadictionaryitem` VALUES (32, b'1', '4001', '4', '违规透传模型', '违规透传');
INSERT INTO `t_datadictionaryitem` VALUES (33, b'1', '4001', '5', '未注册通道模型', '未注册通道');
INSERT INTO `t_datadictionaryitem` VALUES (34, b'1', '4001', '6', '未对标建设模型', '未对标建设');
INSERT INTO `t_datadictionaryitem` VALUES (35, b'1', '4001', '7', '未授权协议模型', '未授权协议');
INSERT INTO `t_datadictionaryitem` VALUES (36, b'1', '4001', '8', '连续流量中断', '连续流量中断');
INSERT INTO `t_datadictionaryitem` VALUES (37, b'1', '4001', '9', '业务日流量超限模型', '业务日流量超限');
INSERT INTO `t_datadictionaryitem` VALUES (38, b'1', '4001', '10', '业务未注册模型', '业务未注册');
INSERT INTO `t_datadictionaryitem` VALUES (39, b'1', '4001', '11', '业务协议不符模型', '业务协议不符');
INSERT INTO `t_datadictionaryitem` VALUES (40, b'1', '4001', '12', '僵尸业务模型', '僵尸业务');
INSERT INTO `t_datadictionaryitem` VALUES (41, b'1', '4001', '13', '业务流量突增模型', '业务流量突增');
INSERT INTO `t_datadictionaryitem` VALUES (42, b'1', '4001', '14', '业务流量突降模型', '业务流量突降');
INSERT INTO `t_datadictionaryitem` VALUES (43, b'1', '4001', '15', '周期流量中断', '周期流量中断');
INSERT INTO `t_datadictionaryitem` VALUES (44, b'1', '4001', '16', '业务数据堆积模型', '业务数据堆积');
INSERT INTO `t_datadictionaryitem` VALUES ('45', b'1', '4002', '1', 'TeamViewer', 'TeamViewer');
INSERT INTO `t_datadictionaryitem` VALUES ('46', b'1', '4002', '2', 'VNC', 'VNC');
INSERT INTO `t_datadictionaryitem` VALUES ('47', b'1', '4002', '3', 'Mining', 'Mining');
INSERT INTO `t_datadictionaryitem` VALUES ('48', b'1', '4002', '4', 'PPTP', 'PPTP');

INSERT INTO `t_datadictionaryitem` VALUES ('49', b'1', '4002', '5', 'Telnet', 'Telnet');
INSERT INTO `t_datadictionaryitem` VALUES ('50', b'1', '4002', '6', 'SSH', 'SSH');
INSERT INTO `t_datadictionaryitem` VALUES ('52', b'1', '4002', '8', 'DNS', 'DNS');
INSERT INTO `t_datadictionaryitem` VALUES ('53', b'1', '4002', '9', 'SMTP', 'SMTP');
INSERT INTO `t_datadictionaryitem` VALUES ('54', b'1', '4002', '10', 'POP3', 'POP3');

INSERT INTO `t_datadictionaryitem` VALUES ('55', b'1', '4004', '1', 'FTP', 'FTP');
INSERT INTO `t_datadictionaryitem` VALUES ('56', b'1', '4004', '2', 'HTTP', 'HTTP');
INSERT INTO `t_datadictionaryitem` VALUES ('57', b'1', '4004', '3', 'MySQL', 'MySQL');
INSERT INTO `t_datadictionaryitem` VALUES ('58', b'1', '4004', '4', 'Oracle', 'Oracle');
INSERT INTO `t_datadictionaryitem` VALUES ('59', b'1', '4004', '5', 'SqlServer', 'SqlServer');
INSERT INTO `t_datadictionaryitem` VALUES ('60', b'1', '4004', '6', 'RTCP', 'RTCP');
INSERT INTO `t_datadictionaryitem` VALUES ('61', b'1', '4005', '1', '链路类', '链路');
INSERT INTO `t_datadictionaryitem` VALUES ('62', b'1', '4005', '2', '业务类', '业务');
INSERT INTO `t_datadictionaryitem` VALUES ('63', b'1', '4005', '3', '设备类', '设备');
INSERT INTO `t_datadictionaryitem` VALUES ('64', b'1', '4005', '4', '访问类', '访问');
INSERT INTO `t_datadictionaryitem` VALUES ('65', b'1', '4005', '5', '其它类', '其它');
INSERT INTO `t_datadictionaryitem` VALUES ('66', b'1', '4006', '1', '占比100%', '100');
INSERT INTO `t_datadictionaryitem` VALUES ('67', b'1', '4006', '2', '占比110%', '110');
INSERT INTO `t_datadictionaryitem` VALUES ('68', b'1', '4006', '3', '占比120%', '120');
INSERT INTO `t_datadictionaryitem` VALUES ('69', b'1', '4006', '4', '占比130%', '130');
INSERT INTO `t_datadictionaryitem` VALUES ('70', b'1', '4006', '5', '占比140%', '140');
INSERT INTO `t_datadictionaryitem` VALUES ('71', b'1', '4006', '6', '占比150%', '150');
INSERT INTO `t_datadictionaryitem` VALUES ('72', b'1', '4007', '1', '占比60%', '60');
INSERT INTO `t_datadictionaryitem` VALUES ('73', b'1', '4007', '2', '占比70%', '70');
INSERT INTO `t_datadictionaryitem` VALUES ('74', b'1', '4007', '3', '占比80%', '80');
INSERT INTO `t_datadictionaryitem` VALUES ('75', b'1', '4007', '4', '占比90%', '90');
INSERT INTO `t_datadictionaryitem` VALUES ('76', b'1', '4007', '5', '占比100%', '100');
INSERT INTO `t_datadictionaryitem` VALUES ('77', b'1', '4008', '1', 'exe,avi,mov,mkv,ogg,qt,asf,rmvb,rm,flv,wmv,mp3,mp4,3gp', '文件后缀');
INSERT INTO `t_datadictionaryitem` VALUES ('78', b'1', '4004', '7', 'FTP_DATA', 'FTP_DATA');
INSERT INTO `t_datadictionaryitem` VALUES ('79', b'1', '4004', '8', 'Telnet', 'Telnet');
INSERT INTO `t_datadictionaryitem` VALUES ('80', b'1', '4004', '9', 'SSH', 'SSH');
INSERT INTO `t_datadictionaryitem` VALUES ('81', b'1', '4004', '10', 'SMTP', 'SMTP');
INSERT INTO `t_datadictionaryitem` VALUES ('82', b'1', '4004', '11', 'RTP', 'RTP');
INSERT INTO `t_datadictionaryitem` VALUES ('83', b'1', '4004', '12', 'RTMP', 'RTMP');
INSERT INTO `t_datadictionaryitem` VALUES ('84', b'1', '4004', '13', 'RTSP', 'RTSP');
INSERT INTO `t_datadictionaryitem` VALUES ('85', b'1', '4004', '14', 'MMS', 'MMS');
INSERT INTO `t_datadictionaryitem` VALUES ('86', b'1', '4004', '15', 'HLS', 'HLS');
INSERT INTO `t_datadictionaryitem` VALUES (87, b'1', '4001', '17', '应用访问异常模型', '应用访问异常');
INSERT INTO `t_datadictionaryitem` VALUES (88, b'1', '4001', '18', '业务日流量流速超限模型', '业务日流量流速超限');
INSERT INTO `t_datadictionaryitem` VALUES (89, b'1', '4001', '19', '设备状态异常模型', '设备状态异常');
INSERT INTO `t_datadictionaryitem` VALUES ('90', b'1', '4007', '6', '占比10%', '10');
INSERT INTO `t_datadictionaryitem` VALUES ('91', b'1', '4007', '7', '占比20%', '20');
INSERT INTO `t_datadictionaryitem` VALUES ('92', b'1', '4007', '8', '占比30%', '30');
INSERT INTO `t_datadictionaryitem` VALUES ('93', b'1', '4007', '9', '占比40%', '40');
INSERT INTO `t_datadictionaryitem` VALUES ('94', b'1', '4007', '10', '占比50%', '50');
INSERT INTO `t_datadictionaryitem` VALUES (95, b'1', '4001', '20', '设备资源不足模型', '设备资源不足');
INSERT INTO `t_datadictionaryitem` VALUES (96, b'1', '4001', '21', '设备资源突增模型', '设备资源突增');
INSERT INTO `t_datadictionaryitem` VALUES ('97', b'1', '4009', '1', '占比100%', '100');
INSERT INTO `t_datadictionaryitem` VALUES ('98', b'1', '4009', '2', '占比200%', '200');
INSERT INTO `t_datadictionaryitem` VALUES ('99', b'1', '4009', '3', '占比300%', '300');
INSERT INTO `t_datadictionaryitem` VALUES ('100', b'1', '4009', '4', '占比400%', '400');
INSERT INTO `t_datadictionaryitem` VALUES ('101', b'1', '4009', '5', '占比500%', '500');
INSERT INTO `t_datadictionaryitem` VALUES ('102', b'1', '4009', '6', '占比600%', '600');
INSERT INTO `t_datadictionaryitem` VALUES ('103', b'1', '4009', '7', '占比700%', '700');
INSERT INTO `t_datadictionaryitem` VALUES ('104', b'1', '4009', '8', '占比800%', '800');
INSERT INTO `t_datadictionaryitem` VALUES ('105', b'1', '4009', '9', '占比900%', '900');
INSERT INTO `t_datadictionaryitem` VALUES ('106', b'1', '4010', '1', 'smsText', 'smsText');
INSERT INTO `t_datadictionaryitem` VALUES ('107', b'1', '4010', '2', 'Content', 'Content');
INSERT INTO `t_datadictionaryitem` VALUES (108, b'1', '4001', '22', '设备资源突降模型', '设备资源突降');
INSERT INTO `t_datadictionaryitem` VALUES (117, b'1', '4001', '23', '设备进程异常模型', '设备进程异常');
INSERT INTO `t_datadictionaryitem` VALUES (118, b'1', '4001', '24', '敏感数据泄露模型', '敏感数据泄露');
INSERT INTO `t_datadictionaryitem` VALUES (119, b'1', '4001', '25', '违规文件类型模型', '违规文件类型');
INSERT INTO `t_datadictionaryitem` VALUES (120, b'1', '4001', '26', '大文件传输模型', '大文件传输');
INSERT INTO `t_datadictionaryitem` VALUES (121, b'1', '4001', '27', '访问外部IP模型', '访问外部IP');
INSERT INTO `t_datadictionaryitem` VALUES (122, b'1', '4001', '28', '异常用户模型', '异常用户');
INSERT INTO `t_datadictionaryitem` VALUES (123, b'1', '4001', '29', '异常文件出网模型', '异常文件出网');
INSERT INTO `t_datadictionaryitem` VALUES (125, b'1', '4001', '30', '疑似僵尸业务', '疑似僵尸业务');
INSERT INTO `t_datadictionaryitem` VALUES (126, b'1', '4001', '31', '边界通道故障', '边界通道故障');
INSERT INTO `t_datadictionaryitem` VALUES (127, b'1', '4001', '32', '边界数据源故障', '边界数据源故障');
INSERT INTO `t_datadictionaryitem` VALUES (128, b'1', '4001', '33', '边界交换中断', '边界交换中断');
INSERT INTO `t_datadictionaryitem` VALUES (129, b'1', '4001', '34', '流量突变告警', '流量突变告警');
INSERT INTO `t_datadictionaryitem` VALUES (130, b'1', '4001', '35', '边界进程故障', '边界进程故障');
INSERT INTO `t_datadictionaryitem` VALUES (131, b'1', '4001', '36', '僵尸链路', '僵尸链路');
INSERT INTO `t_datadictionaryitem` VALUES (132, b'1', '4001', '37', '连续流量过低模型', '连续流量过低');
INSERT INTO `t_datadictionaryitem` VALUES (133, b'1', '1001', '1', '低危', '低危');
INSERT INTO `t_datadictionaryitem` VALUES (134, b'1', '1001', '2', '中危', '中危');
INSERT INTO `t_datadictionaryitem` VALUES (135, b'1', '1001', '3', '高危', '高危');
INSERT INTO `t_datadictionaryitem` VALUES (136, b'1', '1002', '0', '待处置', '待处置');
INSERT INTO `t_datadictionaryitem` VALUES (137, b'1', '1002', '1', '已处置', '已处置');
INSERT INTO `t_datadictionaryitem` VALUES (138, b'1', '1002', '2', '已恢复', '已恢复');
INSERT INTO `t_datadictionaryitem` VALUES (139, b'1', '4002', '11', 'ICMP', 'ICMP');
-- ----------------------------
-- Records of t_permission
-- ----------------------------
delete from `t_permission`;
INSERT INTO `t_permission` VALUES ('1', '1001', '1001', 'Others', '链路中断', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('2', '1002', '1002', 'Others', '边界自检异常', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('3', '1004', '1004', 'Others', '违规透传', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('4', '1005', '1005', 'Others', '未注册通道', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('5', '1006', '1006', 'Others', '未对标建设 ', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('6', '1007', '1007', 'Others', '未授权协议', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('7', '2001', '2001', 'Others', '业务流量中断', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('9', '2003', '2003', 'Others', '业务未注册', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('10', '2004', '2004', 'Others', '业务内容不符', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('11', '2005', '2005', 'Others', '僵尸业务', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('12', '2008', '2008', 'Others', '业务无流量', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('13', '3001', '3001', 'Others', '设备异常', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('14', '3002', '3002', 'Others', '设备资源不足', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('15', '3003', '3003', 'Others', '设备资源突增', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('16', '3004', '3004', 'Others', '设备资源突降', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('17', '3005', '3005', 'Others', '设备进程异常', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('18', '4001', '4001', 'Others', '敏感数据泄露', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
-- 不要删除这些注释，后续可能会重新添加
-- INSERT INTO `t_permission` VALUES ('19', '5001', '5001', 'Others', '违规文件类型', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
-- INSERT INTO `t_permission` VALUES ('20', '5002', '5002', 'Others', '大文件传输', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
-- INSERT INTO `t_permission` VALUES ('21', '5003', '5003', 'Others', '访问外部IP', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
-- INSERT INTO `t_permission` VALUES ('8', '2002', '2002', 'Others', '业务流量异常', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '1');
INSERT INTO `t_permission` VALUES ('22', '5003', '5004', 'Others', '异常用户', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('23', '2009', '2009', 'Others', '业务数据堆积', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO t_permission  VALUES(27,'2010', '2010', 'Others', '应用访问异常', '1', '10', null, '2023-03-09 14:25:11', '2023-03-09 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('28', '2014', '2014', 'Others', 'API请求故障', '1', '10', null, '2024-05-24 14:25:11', '2024-05-24 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('29', '2015', '2015', 'Others', '数据源故障', '1', '10', null, '2024-05-24 14:25:11', '2024-05-24 14:25:11', '0', '2');
INSERT INTO t_permission  VALUES('30','2016', '2016', 'Others', '视频业务异常', '1', '10', null, '2024-05-24 14:25:11', '2024-05-24 14:25:11', '0', '2');
INSERT INTO `t_permission` VALUES ('31', '1008', '1008', 'Others', '边界通道故障', 1, 10, NULL, '2024-06-17 14:25:11', '2024-06-17 14:25:11', 0, 2);
INSERT INTO `t_permission` VALUES ('32', '1009', '1009', 'Others', '边界数据源故障', 1, 10, NULL, '2024-06-17 14:25:11', '2024-06-17 14:25:11', 0, 2);
INSERT INTO `t_permission` VALUES ('33', '1010', '1010', 'Others', '边界交换中断', 1, 10, NULL, '2024-06-17 14:25:11', '2024-06-17 14:25:11', 0, 2);
INSERT INTO `t_permission` VALUES ('34', '2015', '2015', 'Others', '流量突变告警', 1, 10, NULL, '2024-06-17 14:25:11', '2024-06-17 14:25:11', 0, 2);
INSERT INTO `t_permission` VALUES ('35', '3006', '3006', 'Others', '边界进程故障', 1, 10, NULL, '2024-06-17 14:25:11', '2024-06-17 14:25:11', 0, 2);
INSERT INTO `t_permission` VALUES ('36', '1011', '1011', 'Others', '僵尸链路', 1, 10, NULL, '2024-07-25 17:05:11', '2024-07-25 17:05:11', 0, 2);
INSERT INTO `t_permission` VALUES ('37', '2017', '2017', 'Others', '连续流量过低', 1, 10, NULL, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), 0, 2);
-- ----------------------------
-- Records of t_siem_config
-- ----------------------------
INSERT INTO `t_siem_config` VALUES ('1', 'ftp_file', '4000', '大文件报警模型', '2021-06-24 14:41:33');
INSERT INTO `t_siem_config` VALUES ('2', 'ftp_file_suffix', 'exe,avi,mov,mkv,ogg,qt,asf,rmvb,rm,flv,wmv,mp3,mp4,3gp', '文件类型报警模型', '2021-11-19 10:19:48');

update t_event_warn set eventType = 1001 where eventType = 2;
update t_event_warn set eventType = 2003 where eventType = 4;
update t_event_warn set eventType = 2001 where eventType = 5;
update t_event_warn set eventType = 2002 where eventType = 7;
update t_event_warn set eventType = 2006 where eventType = 8;
update t_event_warn set eventType = 3001 where eventType = 9;
update t_event_warn set eventType = 4001 where eventType = 11;
update t_event_warn set eventType = 3002 where eventType = 12;
update t_event_warn set eventType = 2004 where eventType = 14;
update t_event_warn set eventType = 2005 where eventType = 16;
update t_event_warn set eventType = 5003 where eventType = 18;
update t_event_warn set eventType = 2006 where eventType = 19;
update t_event_warn set eventType = 2002 where eventType = 20;
update t_event_warn set eventType = 2002 where eventType = 21;
update t_event_warn set eventType = 1002 where eventType = 22;
update t_alarm_rule set classify = '5' where alarmType = '10';
update t_alarm_rule set classify = '2' where alarmType = '11';

delete from t_event_warn where eventType =2003;
update t_alarm_rule set alarmName = '违规通道',alarmDesc ='违规通道', classify =1 where alarmType =15 ;
update t_alarm_item_plat_rel set item_code = '1004' where item_code ='2003' ;
update t_event_warn set eventType=2002 where eventType=21 ;

-- 更新t_event_warn表中业务未注册的事件类型
UPDATE t_event_warn SET eventType = '2003' WHERE eventType = '2007';

SET FOREIGN_KEY_CHECKS = 1;

REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('109', b'1', '4011', '1', 'SPICE', 'SPICE');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('110', b'1', '4011', '2', 'RDP', 'RDP');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('111', b'1', '4011', '3', 'SOCKS', 'SOCKS');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('114', b'1', '4004', '16', 'SIP', 'SIP');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('115', b'1', '4004', '17', 'PostgreSQL ', 'PostgreSQL ');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('116', b'1', '4004', '18', 'KAFKA ', 'KAFKA ');
REPLACE INTO `t_datadictionaryitem` (`id`, `active`, `ddCode`, `ddItemCode`, `ddItemDiscription`, `ddItemName`) VALUES ('124', b'1', '4011', '7', 'sailing', 'sailing');

INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1001', '{}', 1, 1, 2, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1002', '{}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1003', '{}', 1, 1, 1, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1004', '{\"illegalTransProtocol\":[\"RDP\",\"SOCKS\",\"SPICE\",\"sailing\"]}', 1, 1, 2, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1005', '{}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1006', '{}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1007', '{\"unauthorizedProtocol\":[\"DNS\",\"Mining\",\"POP3\",\"PPTP\",\"SMTP\",\"TeamViewer\",\"VNC\"]}', 1, 1, 2, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1008', '{}', 1, 1, 2, 3,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1009', '{}', 1, 1, 2, 3,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1010', '{}', 1, 1, 2, 3,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1011', '{\"noFlowDays\":10}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2001', '{}', 1, 1, 2, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2002', '{\"flowThreshold\": 200}', 2, 1, 1, 1,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2003', '{\"unregisteredProtocol\":[\"FTP\",\"FTP_DATA\",\"HLS\",\"HTTP\",\"KAFKA\",\"MMS\",\"MySQL\",\"Oracle\",\"PostgreSQL\",\"RTCP\",\"RTMP\",\"RTP\",\"RTSP\",\"SIP\",\"SMTP\",\"SqlServer\",\"SSH\",\"Telnet\"]}', 1, 1, 2, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2004', '{\"inconsistencyProtocol\":[\"FTP\",\"FTP_DATA\",\"HLS\",\"HTTP\",\"KAFKA\",\"MMS\",\"MySQL\",\"Oracle\",\"PostgreSQL\",\"RTCP\",\"RTMP\",\"RTP\",\"RTSP\",\"SIP\",\"SMTP\",\"SqlServer\",\"Telnet\"]}', 1, 1, 2, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2005', '{\"noFlowDays\":10}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2006', '{\"flowIncreaseTime\":240,\"flowIncreaseThreshold\":4000,\"flowIncreaseMultiples\":5}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2007', '{\"flowReduceTime\":240,\"flowReduceThreshold\":4000,\"flowReduceMultiples\":5}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2008', '{}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2009', '{\"msgOffsetStacked\":50,\"msgIncrementalNum\":5,\"fileStacked\":1000,\"fileStackedSize\":5,\"fileIncrementalNum\":5}', 1, 1, 1, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2012', '{}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2014', '{}', 1, 1, 2, 3,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2015', '{}', 1, 1, 2, 2,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2016', '{}', 1, 1, 2, 1,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3001', '{\"pingFailedNum\":3}', 1, 1, 1, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3002', '{\"cpuUsageRate\":90,\"diskUsageRate\":90,\"ramUsageRate\":90,\"netCardLoadRate\":90}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3003', '{\"diskUsageRateI\":200,\"ramUsageRateI\":200}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3004', '{\"ramUsageRateR\":300}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3005', '{}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3006', '{}', 1, 1, 2, 3,'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('4001', '{}', 1, 1, 1, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('4002', '{}', 1, 1, 1, 3, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('5001', '{\"violationFileType\":\"exe,avi,mov,mkv,ogg,qt,asf,rmvb,rm,flv,wmv,mp3,mp4,3gp\"}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('5002', '{\"fileTransThreshold\":8192}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('5003', '{}', 1, 1, 1, 2, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2013', '{}', 1, 1, 1, 1, 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1002', '{}', 1, 1, 1, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1008', '{}', 1, 1, 2, 3,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1009', '{}', 1, 1, 2, 3,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('1010', '{}', 1, 1, 2, 3,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2002', '{\"flowThreshold\": 200}', 2, 1, 1, 1,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2004', '{\"inconsistencyProtocol\":[\"FTP\",\"FTP_DATA\",\"HLS\",\"HTTP\",\"KAFKA\",\"MMS\",\"MySQL\",\"Oracle\",\"PostgreSQL\",\"RTCP\",\"RTMP\",\"RTP\",\"RTSP\",\"SIP\",\"SMTP\",\"SqlServer\",\"Telnet\"]}', 2, 1, 2, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2009', '{\"msgOffsetStacked\":50,\"msgIncrementalNum\":5,\"fileStacked\":1000,\"fileStackedSize\":5,\"fileIncrementalNum\":5}', 1, 1, 1, 3, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2010', '{}', 2, 1, 1, 1,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2011', '{\"flowVelocityThreshold\":81920}', 2, 1, 1, 1,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2014', '{}', 1, 1, 2, 3,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2015', '{}', 1, 1, 2, 2,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2016', '{}', 1, 1, 2, 1,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3001', '{\"pingFailedNum\":3}', 1, 1, 1, 3, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3002', '{\"cpuUsageRate\":90,\"diskUsageRate\":90,\"ramUsageRate\":90,\"netCardLoadRate\":90}', 1, 1, 1, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3003', '{\"diskUsageRateI\":200,\"ramUsageRateI\":200}', 1, 1, 1, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3004', '{\"ramUsageRateR\":300}', 1, 1, 1, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3005', '{}', 1, 1, 1, 2, '7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('3006', '{}', 1, 1, 2, 3,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('5004', '{\"loginFailedTime\":3,\"loginFailedNum\":10}', 2, 1, 1, 2,'7A9DB45DF48E466EB2348FFD5662F71E');
INSERT INTO `t_alarm_rule_config` (`itemCode`, `alarmRule`, `status`, `alarmSwitch`, `analysisType`, `level`, `platId`) VALUES ('2017', '{\"flowThreshold\":4096}', 1, 1, 1, 2,'D0FEBBB132794A00A0A2692195DEB4E7');

INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1001', '0', 'system', 'system', '链路流量中断', '链路上所有连续性业务同时中断，或者边界交换系统心跳中断');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1002', '0', 'system', 'system', '边界自检异常', '边界交换系统定时自检，主动上报自检发现的异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1003', '0', 'system', 'system', '链路自检异常', '边界交换系统定时自检，主动上报自检发现的异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1004', '0', 'system', 'system', '违规透传', '发现通过透传协议进行违规边界访问');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1005', '0', 'system', 'system', '未注册通道', '识别发现未注册的边界设备');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1006', '0', 'system', 'system', '未对标建设', '边界设备注册不全，未按公安部标准建设');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1007', '0', 'system', 'system', '未授权协议', '发现通过远程控制、挖矿等未授权协议进行边界访问');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1008', '1', 'system', 'system', '边界通道故障', '边界设备创建的通道故障');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1009', '1', 'system', 'system', '边界数据源故障', '边界设备数据源故障');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1010', '1', 'system', 'system', '边界交换中断', '边界交换任务中断');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2001', '0', 'system', 'system', '连续流量中断', '连续性业务流量变为0');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2002', '0', 'system', 'system', '业务日流量超限', '实际流量大于等于业务注册字段预估流量的百分比阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2003', '0', 'system', 'system', '业务未注册', '发现未注册地址通过边界传输业务数据');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2004', '0', 'system', 'system', '业务协议不符', '业务实际传输协议和业务注册填报信息不一致');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2005', '0', 'system', 'system', '僵尸业务', '业务连续N天无流量');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2006', '0', 'system', 'system', '业务流量突增', '业务流量 N 分钟内, 流量增加大于 X Mb, 并且超过原来的 Y 倍');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2007', '0', 'system', 'system', '业务流量突降', '业务流量 N 分钟内, 流量减少大于 X Mb, 并且超过原来的 Y 倍');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2008', '0', 'system', 'system', '周期流量中断', '周期性业务在指定的必定有流量周期内无流量');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2009', '0', 'system', 'system', '业务数据堆积', '生产端剩余数据出现堆积且超过阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2010', '0', 'system', 'system', '应用访问异常', '服务异常或网络故障等导致业务数据传输或应用访问出现异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2011', '0', 'system', 'system', '业务日流量流速超限', '五分钟内平均流速超过 N Mb');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2012', '0', 'system', 'system', '疑似僵尸业务', '触发式业务在指定的必定有流量周期内无流量');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2013', '0', 'system', 'system', '疑似注册错误', '疑似业务端口注册错误');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2014', '1', 'system', 'system', 'API请求故障', '目标响应异常码/服务/通道连接故障');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2015', '1', 'system', 'system', '流量突变告警', '边界设备流量发生中断、突增、突降等');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2016', '1', 'system', 'system', '视频业务异常', '视频类业务请求、注册、注销等异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3001', '0', 'system', 'system', '设备状态异常', '监测不到设备心跳');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3002', '0', 'system', 'system', '设备资源不足', '监测到设备CPU、内存、磁盘使用率超出阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3003', '0', 'system', 'system', '设备资源突增', '监测到设备CPU、内存、磁盘使用率短时间突然增加超过阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3004', '0', 'system', 'system', '设备资源突降', '监测到设备CPU、内存、磁盘使用率短时间突然降低超过阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3005', '0', 'system', 'system', '设备进程异常', '监测到设备进程出现假死、反复重启等异常情况');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (3, '3006', '1', 'system', 'system', '边界进程故障', '边界进程异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (4, '4001', '0', 'system', 'system', '敏感数据泄露', '出网数据中监测到银行卡号、身份证号、车牌号等敏感信息');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (4, '4002', '0', 'system', 'system', '异常文件出网', '出网文件格式、文件名称长度、文件大小异常');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (5, '5001', '0', 'system', 'system', '违规文件类型', '传输文件类型属于异常文件格式');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (5, '5002', '0', 'system', 'system', '大文件传输', '大文件传输报警阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (5, '5003', '0', 'system', 'system', '访问外部IP', '访问外部IP');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (5, '5004', '0', 'system', 'system', '异常用户', '同一用户登录失败次数超过阈值');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (1, '1011', '0', 'system', 'system', '僵尸链路', '链路未注册业务或者所有业务连续N天无流量');
INSERT INTO `t_alarm_item` (`model_type`, `item_code`, `itemType`, `create_user`, `modify_user`, `name`, `description`) VALUES (2, '2017', '0', 'system', 'system', '连续流量过低', '连续性业务的流量低于阈值 N Mb');

INSERT INTO `t_alarm_group` (`id`, `groupName`, `status`, `platId`) VALUES (1, '默认分组', b'1', 'D0FEBBB132794A00A0A2692195DEB4E7');
INSERT INTO `t_alarm_group` (`id`, `groupName`, `status`, `platId`) VALUES (2, '默认分组', b'1', '7A9DB45DF48E466EB2348FFD5662F71E');

INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (1, '链路异常', 1, '1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (2, '业务异常', 1, '2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (3, '设备异常', 1, '3001,3002,3003,3004,3005,3006');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (4, '数据泄露', 1, '4001,4002');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (5, '其他异常', 1, '5001,5002,5003,5004');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (6, '链路异常', 2, '1002,1008,1009,1010');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (7, '业务异常', 2, '2002,2004,2009,2010,2011,2014,2015,2016,2017');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (8, '设备异常', 2, '3001,3002,3003,3004,3005,3006');
INSERT INTO `t_alarm_classify` (`id`, `classifyName`, `groupId`, `alarmItemCodes`) VALUES (9, '其他异常', 2, '5004');
INSERT INTO `t_alarm_event_strategy` (`id`, `strategyName`, `strategyDesc`, `timeWindow`, `status`, `priority`, `eventLevel`, `config`, `eventExplainTemplate`, `eventDescTemplate`, `handlingSuggestionTemplate`, `createTime`, `updateTime`, `remarks`) VALUES (1, '设备硬件故障引发流量中断', '', 1200, 1, 1, 3, '{"evnetType":[{"alarmName":"设备状态异常","itemCode":"3001"},{"alarmName":"连续流量中断","itemCode":"2001"},{  "alarmName": "业务流量突增", "itemCode": "2006"  },{ "alarmName": "业务流量突降", "itemCode": "2007" },{ "alarmName": "业务数据堆积", "itemCode": "2009" },{"alarmName":"连续流量过低",  "itemCode": "2017"  },{"alarmName":"链路流量中断","itemCode":"1001"}]}', '{"base":"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备状态异常】告警。\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\n【appAlarmTime3】：还触发了【appTypeName3】告警。\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\n还有appNames等其他业务也受到了影响，流量降为0Mb。\n","hardware":"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\n还有appNames等其他业务也受到了影响，流量降为0Mb。"}', '{"hardware":"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了设备硬件故障引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】"}', '{"base":"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n","hardware":"设备硬件故障：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请核实【deviceType】【deviceIp】地址信息，是否重复分配；\\n④请在【deviceType】【deviceIp】后台使用ethtool等命令行工具查看网卡硬件状态；\\n⑤检查设备电源是否正常供电。\\n"}', '2024-12-09 17:09:40', '2025-04-28 10:03:30', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (2, '资源不足引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备资源不足\",\"itemCode\":\"3002\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\"base\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\r\n\"}', '{\"cpu\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了CPU资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"disk\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了磁盘资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"memory\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了内存资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{\"base\":\"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\"cpu\":\"CPU：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前CPU资源利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查CPU散热系统是否良好；\\n⑤确保断电后，升级CPU的硬件配置。\\n\",\"disk\":\"磁盘：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行df -h命令，核查设备当前磁盘资源利用率是否正处于高位；\\n④请在【deviceType】【deviceIp】清理磁盘空间，删除不必要的文件；\\n⑤增加更多的磁盘空间，并使用磁盘监控工具来识别和解决高I/O的问题。\\n\",\"memory\":\"内存：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前内存利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查内存散热系统是否良好；\\n⑤确保断电后，确认主板是否具备空余内存插槽，增加同频率同容量内存条。\\n\"}', '2024-12-11 10:31:42', '2025-04-10 15:51:30', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (3, '进程故障引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备进程异常\",\"itemCode\":\"3005\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\"process\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\"}', '{\"process\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了设备进程异常引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{  \"base\": \"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\r\n    \"process\": \"进程故障：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③核查【deviceType】【deviceIp】的【processName】，终止或重启非正常或僵尸进程；\\n④请检查任务当前节点编号的进程是否存在，可能是缺少配置导致启动失败，例如单向链路前置部署，后置未部署会导致这种情况发生。\\n\"}', '2024-12-11 10:31:42', '2025-04-10 15:52:32', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (4, '违规透传通道威胁内网安全', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"违规透传通道威胁内网安全\",\"itemCode\":\"1004\"}]}', '{\"illefal\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下违规透传事件：\\n【linkAlarmTime】：涉及的设备类型为【边界前置机】设备(IP deviceIp1，透传端口:devicePort1)，违规访问内网服务器deviceIp2的【devicePort2端口】，建立非法通信通道。\r\n\"}', '{\"illefal\":\"【linkAlarmTime】，因【uniPositionName】【domainName】当中的【边界前置机】，设备ip为【deviceIp1】，存在端口为：devicePort1的违规透传通道，进而导致【linkName】链路发生【违规透传】\"}\r\n', '{  \"base\": \"为确保设备尽快恢复正常运行、减少停机时间并保障业务合规性，请联系对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【边界前置机】（deviceIp1）根据以下措施执行：\\n\",                     \r\n    \"illefal\": \"①立即响应封禁外交换服务器的devicePort1端口通信。\\n②登录服务器核查日志： 登录【边界前置机】（deviceIp1），查看边界交换日志，验证访问源【deviceIp1:devicePort1】是否对内网【deviceIp2:devicePort2】存在更多违规操作。\\n③取证与关闭通道： 完成边界交换日志的取证后，立即关闭违规透传通道，确保内网安全。\\n\"}\r\n	', '2025-03-12 10:22:01', '2025-04-10 15:53:12', '1');
INSERT INTO `t_alarm_event_strategy` VALUES (5, '违规未注册链路', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"违规未注册链路\",\"itemCode\":\"1005\"}]}', '{\"unregistered\":\"【uniPositionName】中，检测识别到以下风险，检测路径如下：\\n【taskStartTime】：用户创建【taskName】对未注册链路启动检测。\\n【taskStopTime】：检测任务识别到IP范围为【borderIp】，IP数量共计有【ipCount】个，其中网络可达IP数量为【accessibleIpCount】个，网络可达率为【achievableRate%】，并检测识别到存在【linkCount】个未注册链路。\\n\",\"base\":\"【alarmTime】：链路IP为【borderIp】，开放端口为【borderPort】所属厂商为【manufacturerName】，设备类型为【deviceType】。\\n\"}', '{\"unregistered\":\"【taskStartTime】，【uniPositionName】的用户启用了未注册链路检测，于【taskStopTime】检测到共计存在linkCount起未注册链路告警。\"}', '{\"base\":\"为确保尽快消除违规数据传输风险并保障链路合规可控，请联系以下厂商关停链路，并提交链路审批流程后启用链路，相关信息如下：\\n\",\"unregistered\":\"【manufacturerName】需核查assetTypeAndIps的边界设备。\\n\"}', '2025-03-12 10:22:01', '2025-04-15 17:20:50', '1');
INSERT INTO `t_alarm_event_strategy` VALUES (6, '敏感数据出网', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"敏感数据出网\",\"itemCode\":\"4001\"}]}', '{\"base\":\"【uniPositionName】【domainName】中，检测识别到【linkName】的【appName】存在以下按时间顺序发生的transMethod数据泄露风险：\\n\",\"sensitive\":\"泄露源IP：【srcIp】，目标IP：【dstIp】\\n【alarmTime1】起，文件名为【fileName1】发生数据泄露，涉及violatContent1等敏感内容。\\n【alarmTime2】起：文件名为【fileName2】发生数据泄露，涉及violatContent2等敏感内容。\\n【alarmTime3】起：文件名为【fileName3】发生数据泄露，涉及violatContent3等敏感内容。\\n【alarmTime4】起：文件名为【fileName4】发生数据泄露，涉及violatContent4等敏感内容。\\n【alarmTime5】起：文件名为【fileName5】发生数据泄露，涉及violatContent5等敏感内容。\r\n\"}', '{\"sensitive\":\"【firstAlarmTime】，识别到【uniPositionName】【domainName】的【linkName】当中的【appName】，涉及alarmCount起敏感数据传输出网。\"}\r\n', '{  \"base\": \"为防止数据泄露范围扩大，请立即联系业务责任人【name】【phone】对上述文件fileNames进行实时监测及回溯，\\n\",                     \r\n    \"sensitive\": \"检查文件访问记录、权限分配及异常操作日志，确认是否存在未授权访问或内部人员违规行为。\\n\"}', '2025-03-12 10:22:01', '2025-04-14 15:15:58', '1');

UPDATE `t_permission` SET name = '未注册链路' WHERE id = 4;
UPDATE `t_permission` SET name = '未注册业务' WHERE id = 9;
UPDATE `t_alarm_item` SET name = '未注册链路' WHERE item_code = '1005';
UPDATE `t_alarm_item` SET name = '未注册业务' WHERE item_code = '2003';
UPDATE `t_datadictionaryitem` SET ddItemCode = '未注册链路', ddItemDiscription = '未注册链路模型' WHERE id = 33;
UPDATE `t_datadictionaryitem` SET ddItemCode = '未注册业务', ddItemDiscription = '未注册业务模型' WHERE id = 38;