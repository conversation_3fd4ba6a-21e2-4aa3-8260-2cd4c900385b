-- 告警聚合
DROP TABLE IF EXISTS `siem`.`t_alarm_aggregation_rule`;
CREATE TABLE `t_alarm_aggregation_rule` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                            `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '聚合规则配置',
                                            `aggregationRule` varchar(255) NOT NULL COMMENT '聚合纬度',
                                            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
                                            `createTime` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
                                            `updateTime` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='告警聚合规则表';

DROP TABLE IF EXISTS `siem`.`t_alarm_mid_aggregation`;
CREATE TABLE `t_alarm_mid_aggregation` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                           `uniPositionCode` varchar(20) NOT NULL COMMENT '站点编号',
                                           `alarmEventId` bigint(20) DEFAULT NULL COMMENT '安全事件id',
                                           `alarmId` bigint(20) NOT NULL COMMENT '告警id',
                                           `alarmIds` varchar(200) DEFAULT NULL COMMENT '告警聚合ids',
                                           `alarmType` varchar(200) DEFAULT NULL COMMENT '聚合告警类型',
                                           `appId` varchar(50) DEFAULT NULL COMMENT '业务id',
                                           `linkId` varchar(50) DEFAULT NULL COMMENT '链路id',
                                           `deviceIp` varchar(50) DEFAULT NULL COMMENT '设备ip',
                                           `createTime` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
                                           `updateTime` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='告警聚合id中间表';

delete from `t_alarm_event_strategy` where id in (2,3);
delete from `t_alarm_aggregation_rule` where id in (1,2,3,4,5,6);


INSERT INTO `t_alarm_aggregation_rule` VALUES (1, '2006,2007,2009,2017,2001,2008,3005,3004,1001,1002', 'link', 1, '2025-05-16 17:40:54', '2025-05-22 11:14:35');
INSERT INTO `t_alarm_aggregation_rule` VALUES (3, '2006,2007,2009,2017,2001,2008', 'app', 1, '2025-05-16 17:41:30', '2025-05-22 11:14:38');
INSERT INTO `t_alarm_aggregation_rule` VALUES (4, '1005,1004,1007,5003,4002,4001', 'device', 1, '2025-05-16 17:44:59', '2025-05-19 16:13:00');
INSERT INTO `t_alarm_aggregation_rule` VALUES (5, '1006,2004,1004,1007,5003,4002,4001', 'device', 1, '2025-05-16 17:45:53', '2025-05-19 16:13:02');
INSERT INTO `t_alarm_aggregation_rule` VALUES (6, '2005,1011', 'link', 1, '2025-05-16 17:48:10', '2025-05-19 16:04:18');

ALTER TABLE `siem`.`t_alarm_event_strategy`
    MODIFY COLUMN IF EXISTS `eventExplainTemplate` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件解读还原模板';

INSERT INTO `t_alarm_event_strategy` VALUES (2, '资源不足引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备资源不足\",\"itemCode\":\"3002\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{  \"alarmName\": \"业务流量突增\", \"itemCode\": \"2006\"  },{ \"alarmName\": \"业务流量突降\", \"itemCode\": \"2007\" },{ \"alarmName\": \"业务数据堆积\", \"itemCode\": \"2009\" },{\"alarmName\":\"连续流量过低\",  \"itemCode\": \"2017\"  },{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\r\n    \"cpu\": \"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备CPU资源不足】告警。\\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\\n【appAlarmTime3】：还触发了【appTypeName3】告警。\\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\\n还有appNames等其他业务也受到了影响，流量降为0Mb\",\r\n    \"memory\": \"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备内存资源不足】告警。\\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\\n【appAlarmTime3】：还触发了【appTypeName3】告警。\\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\\n还有appNames等其他业务也受到了影响，流量降为0Mb\",\r\n    \"disk\": \"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备磁盘资源不足】告警。\\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\\n【appAlarmTime3】：还触发了【appTypeName3】告警。\\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\\n还有appNames等其他业务也受到了影响，流量降为0Mb\",\r\n    \"base\": \"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\"\r\n}', '{\"cpu\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了CPU资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"disk\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了磁盘资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\",\"memory\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了内存资源不足引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{\"base\":\"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\"cpu\":\"CPU：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前CPU资源利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查CPU散热系统是否良好；\\n⑤确保断电后，升级CPU的硬件配置。\\n\",\"disk\":\"磁盘：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行df -h命令，核查设备当前磁盘资源利用率是否正处于高位；\\n④请在【deviceType】【deviceIp】清理磁盘空间，删除不必要的文件；\\n⑤增加更多的磁盘空间，并使用磁盘监控工具来识别和解决高I/O的问题。\\n\",\"memory\":\"内存：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③请在【deviceType】【deviceIp】后台执行top命令，核查设备当前内存利用率是否持续处于高位；\\n④请在【deviceType】【deviceIp】后台使用lm-sensors（硬件传感器监测工具）检查内存散热系统是否良好；\\n⑤确保断电后，确认主板是否具备空余内存插槽，增加同频率同容量内存条。\\n\"}', '2024-12-11 10:31:42', '2025-05-15 16:57:49', '2');
INSERT INTO `t_alarm_event_strategy` VALUES (3, '进程故障引发流量中断', '', 1200, 1, 1, 3, '{\"evnetType\":[{\"alarmName\":\"设备进程异常\",\"itemCode\":\"3005\"},{\"alarmName\":\"连续流量中断\",\"itemCode\":\"2001\"},{\"alarmName\":\"链路流量中断\",\"itemCode\":\"1001\"}]}', '{\"process\":\"【uniPositionName】在【domainName】的【linkName】中，监测到设备故障：\r\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【设备进程异常】告警。\r\n【deviceAlarmTime】：紧接着，该链路持续不断出现流量波动迹象，并且业务也受到影响。典型业务受影响情况按时间顺序梳理如下：\r\n【systemName】业务（业务IP：【appIp】，端口：【appPort】）\r\n【appAlarmTime1】：检测到出现【appTypeName1】告警。\r\n【appAlarmTime2】：紧接着出现【appTypeName2】告警。\r\n【appAlarmTime3】：还触发了【appTypeName3】告警。\r\n【appAlarmTime4】：最终触发了【appTypeName4】告警。\r\n还有appNames等其他业务也受到了影响，流量降为0Mb。\r\n\",\"base\":\"【uniPositionName】在【domainName】的【linkName】中，监测到以下按时间顺序发生的事件：\\n【deviceAlarmTime】：一台【deviceType】设备（IP：【deviceIp】）触发了【deviceModelName】告警。\\n【linkAlarmTime】：紧接着，分析识别到该链路开始出现中断迹象，并且业务流量也受到影响。该链路上的业务流量具体受影响情况如下：\\n【appAlarmTime1】：【appName1】业务（业务IP：【appIp1】，端口：【appPort1】）的流量降为0Mb。\\n【appAlarmTime2】：【appName2】业务（业务IP：【appIp2】，端口：【appPort2】）的流量也降为0Mb。\\n【appAlarmTime3】：【appName3】业务（业务IP：【appIp3】，端口：【appPort3】）的流量同样降为0Mb。\\n还有appNames等其他业务也受到了影响，流量降为0Mb。\"}', '{\"process\":\"【deviceAlarmTime】，因【uniPositionName】【domainName】当中的【deviceType】，设备ip为【deviceIp】， 发生了设备进程异常引发流量中断事件，进而导致【linkName】链路于【appAlarmTime】发生【链路中断/业务流量中断】\"}', '{  \"base\": \"为确保设备能够尽快恢复正常运行、减少停机时间并保障业务稳定，请联系设备对应链路责任人：【manufacturerPerson】以及【policeName】警官。对【deviceType】（deviceIp）根据以下改进措施进行检查：\\n\",\r\n    \"process\": \"进程故障：\\n①核查【deviceType】【deviceIp】设备在 【deviceAlarmTime】的流量趋势；\\n②请登录【deviceType】【deviceIp】检查同网段设备网络连通性,检查物理连接，如网线、电源线，确保所有物理连接牢固无误；\\n③核查【deviceType】【deviceIp】的【processName】，终止或重启非正常或僵尸进程；\\n④请检查任务当前节点编号的进程是否存在，可能是缺少配置导致启动失败，例如单向链路前置部署，后置未部署会导致这种情况发生。\\n\"}', '2024-12-11 10:31:42', '2025-05-21 14:43:45', '2');
